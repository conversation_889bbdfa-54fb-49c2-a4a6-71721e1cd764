@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic font support */
.font-arabic {
  font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
}

/* RTL support improvements */
[dir="rtl"] {
  font-family: '<PERSON><PERSON>wal', 'Arial', sans-serif;
}

/* Enhanced RTL Layout Support */
[dir="rtl"] * {
  font-family: 'Tajawal', 'Arial', sans-serif;
}

[dir="rtl"] .text-right {
  text-align: right !important;
}

[dir="rtl"] .text-left {
  text-align: left !important;
}

/* RTL Input and Form Elements */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right !important;
  direction: rtl !important;
}

/* RTL Button and Control Alignment */
[dir="rtl"] button {
  font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
}

/* Icon positioning for Arabic - only reverse icon+text combinations */
[dir="rtl"] .icon-before-text {
  flex-direction: row-reverse;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* تعريف متغيرات الألوان للـ light mode */
:root.light {
  --background: #ffffff;
  --foreground: #171717;
}

/* تعريف متغيرات الألوان للـ dark mode */
:root.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

/* إزالة الاعتماد على system preference */
body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* تأكيد تطبيق الألوان حسب الـ theme */
[data-theme="light"] {
  --background: #ffffff;
  --foreground: #171717;
}

[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #ededed;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
