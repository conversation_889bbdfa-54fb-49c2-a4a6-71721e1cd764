"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/final-preview/page",{

/***/ "(app-pages-browser)/./src/app/final-preview/page.tsx":
/*!****************************************!*\
  !*** ./src/app/final-preview/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FinalPreview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction FinalPreview() {\n    _s();\n    const { getAllData, currentLanguage, outputFormat, setOutputFormat, projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const modules = [\n        {\n            key: \"projectDefinition\",\n            name: \"Project Definition\",\n            nameAr: \"تعريف المشروع\",\n            emoji: \"\\uD83C\\uDFAF\",\n            data: projectDefinition,\n            href: \"/project-definition\"\n        },\n        {\n            key: \"contextMap\",\n            name: \"Context Map\",\n            nameAr: \"خريطة السياق\",\n            emoji: \"\\uD83D\\uDDFA️\",\n            data: contextMap,\n            href: \"/context-map\"\n        },\n        {\n            key: \"emotionalTone\",\n            name: \"Emotional Tone\",\n            nameAr: \"النبرة العاطفية\",\n            emoji: \"✨\",\n            data: emotionalTone,\n            href: \"/emotional-tone\"\n        },\n        {\n            key: \"technicalLayer\",\n            name: \"Technical Layer\",\n            nameAr: \"الطبقة التقنية\",\n            emoji: \"⚙️\",\n            data: technicalLayer,\n            href: \"/technical-layer\"\n        },\n        {\n            key: \"legalRisk\",\n            name: \"Legal & Privacy\",\n            nameAr: \"القانونية والخصوصية\",\n            emoji: \"\\uD83D\\uDD12\",\n            data: legalRisk,\n            href: \"/legal-risk\"\n        }\n    ];\n    const generateCompleteOutput = ()=>{\n        const allData = getAllData();\n        if (outputFormat === \"markdown\") {\n            let markdown = \"# ContextKit - Complete AI Project Context\\n\\n\";\n            markdown += \"Generated on: \".concat(new Date().toLocaleDateString(), \"\\n\\n\");\n            modules.forEach((module)=>{\n                const title = isArabic ? module.nameAr : module.name;\n                markdown += \"## \".concat(module.emoji, \" \").concat(title, \"\\n\\n\");\n                Object.entries(module.data).forEach((param)=>{\n                    let [key, value] = param;\n                    if (value && typeof value === \"string\" && value.trim()) {\n                        const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                        markdown += \"### \".concat(formattedKey, \"\\n\").concat(value, \"\\n\\n\");\n                    }\n                });\n            });\n            return markdown;\n        }\n        if (outputFormat === \"json\") {\n            return JSON.stringify({\n                contextKit: {\n                    metadata: {\n                        generatedAt: new Date().toISOString(),\n                        language: currentLanguage,\n                        version: \"1.0\"\n                    },\n                    modules: allData\n                }\n            }, null, 2);\n        }\n        if (outputFormat === \"html\") {\n            let html = '<!DOCTYPE html>\\n<html lang=\"'.concat(currentLanguage, '\">\\n<head>\\n');\n            html += '  <meta charset=\"UTF-8\">\\n';\n            html += \"  <title>ContextKit - AI Project Context</title>\\n\";\n            html += \"  <style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>\\n\";\n            html += \"</head>\\n<body>\\n\";\n            html += \"  <h1>\\uD83E\\uDDE0 ContextKit - Complete AI Project Context</h1>\\n\";\n            html += \"  <p><em>Generated on: \".concat(new Date().toLocaleDateString(), \"</em></p>\\n\\n\");\n            modules.forEach((module)=>{\n                const title = isArabic ? module.nameAr : module.name;\n                html += \"  <section>\\n    <h2>\".concat(module.emoji, \" \").concat(title, \"</h2>\\n\");\n                Object.entries(module.data).forEach((param)=>{\n                    let [key, value] = param;\n                    if (value && typeof value === \"string\" && value.trim()) {\n                        const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                        html += \"    <h3>\".concat(formattedKey, \"</h3>\\n    <p>\").concat(value, \"</p>\\n\");\n                    }\n                });\n                html += \"  </section>\\n\\n\";\n            });\n            html += \"</body>\\n</html>\";\n            return html;\n        }\n        return \"\";\n    };\n    const handleCopyAll = async ()=>{\n        const output = generateCompleteOutput();\n        await navigator.clipboard.writeText(output);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    const handleDownload = ()=>{\n        const output = generateCompleteOutput();\n        const extension = outputFormat === \"json\" ? \"json\" : outputFormat === \"html\" ? \"html\" : \"md\";\n        const filename = \"contextkit-complete.\".concat(extension);\n        const blob = new Blob([\n            output\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const hasAnyData = modules.some((module)=>Object.values(module.data).some((value)=>value && typeof value === \"string\" && value.trim()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: isArabic ? \"المعاينة النهائية\" : \"Final Preview\",\n                    subtitle: isArabic ? \"راجع واستخرج السياق الكامل لمشروعك\" : \"Review and export your complete project context\",\n                    emoji: \"\\uD83D\\uDCCB\",\n                    backLink: {\n                        href: \"/emotional-tone\",\n                        label: isArabic ? \"العودة للنبرة العاطفية\" : \"Back to Emotional Tone\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center\",\n                            children: isArabic ? \"نظرة عامة على المحاور\" : \"Modules Overview\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: modules.map((module)=>{\n                                const hasData = Object.values(module.data).some((value)=>value && typeof value === \"string\" && value.trim());\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl \".concat(isArabic ? \"ml-3\" : \"mr-3\"),\n                                                            children: module.emoji\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                            children: isArabic ? module.nameAr : module.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full \".concat(hasData ? \"bg-green-500\" : \"bg-gray-300\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                            children: hasData ? isArabic ? \"مكتمل\" : \"Completed\" : isArabic ? \"غير مكتمل\" : \"Incomplete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: module.href,\n                                            className: \"text-blue-600 dark:text-blue-400 hover:underline text-sm\",\n                                            children: [\n                                                isArabic ? \"تعديل\" : \"Edit\",\n                                                \" →\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, module.key, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                hasAnyData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: isArabic ? \"تصدير السياق الكامل\" : \"Export Complete Context\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-3 \".concat(isArabic ? \"justify-end\" : \"justify-start\"),\n                                        children: [\n                                            \"markdown\",\n                                            \"html\",\n                                            \"json\"\n                                        ].map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setOutputFormat(format),\n                                                className: \"group relative px-8 py-4 text-sm font-medium rounded-xl transition-all duration-300 ease-in-out backdrop-blur-md border overflow-hidden \".concat(outputFormat === format ? \"border-blue-400/50 bg-gradient-to-br from-blue-500/80 via-indigo-500/80 to-purple-600/80 text-white shadow-lg shadow-blue-500/25 scale-105\" : \"border-gray-300/50 dark:border-gray-600/50 bg-white/60 dark:bg-gray-800/60 text-gray-700 dark:text-gray-300 hover:bg-white/80 dark:hover:bg-gray-700/80 hover:border-blue-300/50 dark:hover:border-blue-500/50 hover:scale-105 hover:shadow-md\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative font-arabic\",\n                                                        children: format.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    outputFormat === format && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 left-0 w-full h-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-3 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\",\n                                                                style: {\n                                                                    animationDelay: \"0.2s\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-1 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\",\n                                                                style: {\n                                                                    animationDelay: \"0.4s\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, format, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                                children: [\n                                                    outputFormat === \"markdown\" && (isArabic ? \"تنسيق Markdown - جاهز للاستخدام في المستندات\" : \"Markdown format - Ready for documentation\"),\n                                                    outputFormat === \"html\" && (isArabic ? \"تنسيق HTML - جاهز للمواقع الإلكترونية\" : \"HTML format - Ready for websites\"),\n                                                    outputFormat === \"json\" && (isArabic ? \"تنسيق JSON - جاهز للبرمجة والـ APIs\" : \"JSON format - Ready for programming and APIs\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3 \".concat(isArabic ? \"space-x-reverse space-x-3\" : \"space-x-3\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCopyAll,\n                                                        className: \"group relative flex items-center gap-3 px-8 py-4 text-sm font-medium rounded-xl transition-all duration-300 ease-in-out backdrop-blur-md border border-green-400/50 overflow-hidden bg-gradient-to-br from-green-500/80 via-emerald-500/80 to-teal-600/80 hover:from-green-600/90 hover:via-emerald-600/90 hover:to-teal-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-green-500/25 hover:scale-105 active:scale-95\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg \".concat(copied ? \"animate-bounce\" : \"group-hover:scale-110 transition-transform duration-300\"),\n                                                                        children: \"\\uD83D\\uDCCB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-arabic\",\n                                                                        children: copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ الكل\" : \"Copy All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-0 left-0 w-full h-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-3 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\",\n                                                                        style: {\n                                                                            animationDelay: \"0.2s\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\",\n                                                                        style: {\n                                                                            animationDelay: \"0.4s\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleDownload,\n                                                        className: \"group relative flex items-center gap-3 px-8 py-4 text-sm font-medium rounded-xl transition-all duration-300 ease-in-out backdrop-blur-md border border-blue-400/50 overflow-hidden bg-gradient-to-br from-blue-500/80 via-indigo-500/80 to-purple-600/80 hover:from-blue-600/90 hover:via-indigo-600/90 hover:to-purple-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-blue-500/25 hover:scale-105 active:scale-95\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: \"\\uD83D\\uDCBE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-arabic\",\n                                                                        children: isArabic ? \"تحميل\" : \"Download\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-0 left-0 w-full h-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-3 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\",\n                                                                        style: {\n                                                                            animationDelay: \"0.2s\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping\",\n                                                                        style: {\n                                                                            animationDelay: \"0.4s\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-6 max-h-96 overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                    children: generateCompleteOutput()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this),\n                !hasAnyData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-6xl mb-4 block\",\n                                children: \"\\uD83D\\uDCDD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: isArabic ? \"لا توجد بيانات للمعاينة\" : \"No Data to Preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                children: isArabic ? \"ابدأ بملء المحاور المختلفة لرؤية المعاينة النهائية\" : \"Start filling out the different modules to see the final preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/project-definition\",\n                                className: \"inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                                children: isArabic ? \"ابدأ من تعريف المشروع\" : \"Start with Project Definition\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(FinalPreview, \"BUDy2Y3nDvIOmux8e2nHJWCyx4c=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore\n    ];\n});\n_c = FinalPreview;\nvar _c;\n$RefreshReg$(_c, \"FinalPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/final-preview/page.tsx\n"));

/***/ })

});