'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import { useContextStore } from '@/store/contextStore';

export default function FinalPreview() {
  const { 
    getAllData, 
    currentLanguage, 
    outputFormat, 
    setOutputFormat,
    projectDefinition,
    contextMap,
    emotionalTone,
    technicalLayer,
    legalRisk
  } = useContextStore();
  
  const [copied, setCopied] = useState(false);
  const isArabic = currentLanguage === 'ar';

  const modules = [
    {
      key: 'projectDefinition',
      name: 'Project Definition',
      nameAr: 'تعريف المشروع',
      emoji: '🎯',
      data: projectDefinition,
      href: '/project-definition'
    },
    {
      key: 'contextMap',
      name: 'Context Map',
      nameAr: 'خريطة السياق',
      emoji: '🗺️',
      data: contextMap,
      href: '/context-map'
    },
    {
      key: 'emotionalTone',
      name: 'Emotional Tone',
      nameAr: 'النبرة العاطفية',
      emoji: '✨',
      data: emotionalTone,
      href: '/emotional-tone'
    },
    {
      key: 'technicalLayer',
      name: 'Technical Layer',
      nameAr: 'الطبقة التقنية',
      emoji: '⚙️',
      data: technicalLayer,
      href: '/technical-layer'
    },
    {
      key: 'legalRisk',
      name: 'Legal & Privacy',
      nameAr: 'القانونية والخصوصية',
      emoji: '🔒',
      data: legalRisk,
      href: '/legal-risk'
    }
  ];

  const generateCompleteOutput = () => {
    const allData = getAllData();
    
    if (outputFormat === 'markdown') {
      let markdown = `# ContextKit - Complete AI Project Context\n\n`;
      markdown += `Generated on: ${new Date().toLocaleDateString()}\n\n`;
      
      modules.forEach(module => {
        const title = isArabic ? module.nameAr : module.name;
        markdown += `## ${module.emoji} ${title}\n\n`;
        
        Object.entries(module.data).forEach(([key, value]) => {
          if (value && typeof value === 'string' && value.trim()) {
            const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            markdown += `### ${formattedKey}\n${value}\n\n`;
          }
        });
      });
      
      return markdown;
    }
    
    if (outputFormat === 'json') {
      return JSON.stringify({
        contextKit: {
          metadata: {
            generatedAt: new Date().toISOString(),
            language: currentLanguage,
            version: '1.0'
          },
          modules: allData
        }
      }, null, 2);
    }
    
    if (outputFormat === 'html') {
      let html = `<!DOCTYPE html>\n<html lang="${currentLanguage}">\n<head>\n`;
      html += `  <meta charset="UTF-8">\n`;
      html += `  <title>ContextKit - AI Project Context</title>\n`;
      html += `  <style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>\n`;
      html += `</head>\n<body>\n`;
      html += `  <h1>🧠 ContextKit - Complete AI Project Context</h1>\n`;
      html += `  <p><em>Generated on: ${new Date().toLocaleDateString()}</em></p>\n\n`;
      
      modules.forEach(module => {
        const title = isArabic ? module.nameAr : module.name;
        html += `  <section>\n    <h2>${module.emoji} ${title}</h2>\n`;
        
        Object.entries(module.data).forEach(([key, value]) => {
          if (value && typeof value === 'string' && value.trim()) {
            const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            html += `    <h3>${formattedKey}</h3>\n    <p>${value}</p>\n`;
          }
        });
        
        html += `  </section>\n\n`;
      });
      
      html += `</body>\n</html>`;
      return html;
    }
    
    return '';
  };

  const handleCopyAll = async () => {
    const output = generateCompleteOutput();
    await navigator.clipboard.writeText(output);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleDownload = () => {
    const output = generateCompleteOutput();
    const extension = outputFormat === 'json' ? 'json' : outputFormat === 'html' ? 'html' : 'md';
    const filename = `contextkit-complete.${extension}`;
    
    const blob = new Blob([output], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const hasAnyData = modules.some(module => 
    Object.values(module.data).some(value => 
      value && typeof value === 'string' && value.trim()
    )
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800" dir={isArabic ? 'rtl' : 'ltr'}>
      <div className="container mx-auto px-4 py-8">
        <Header
          title={isArabic ? "المعاينة النهائية" : "Final Preview"}
          subtitle={isArabic ? "راجع واستخرج السياق الكامل لمشروعك" : "Review and export your complete project context"}
          emoji="📋"
          backLink={{
            href: "/emotional-tone",
            label: isArabic ? "العودة للنبرة العاطفية" : "Back to Emotional Tone"
          }}
        />

        {/* Modules Overview */}
        <div className="max-w-6xl mx-auto mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
            {isArabic ? "نظرة عامة على المحاور" : "Modules Overview"}
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {modules.map((module) => {
              const hasData = Object.values(module.data).some(value =>
                value && typeof value === 'string' && value.trim()
              );

              return (
                <div key={module.key} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <div className={`flex items-center justify-between mb-4 ${isArabic ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center ${isArabic ? 'flex-row-reverse' : ''}`}>
                      <span className={`text-2xl ${isArabic ? 'ml-3' : 'mr-3'}`}>{module.emoji}</span>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {isArabic ? module.nameAr : module.name}
                      </h3>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${hasData ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {hasData 
                      ? (isArabic ? "مكتمل" : "Completed")
                      : (isArabic ? "غير مكتمل" : "Incomplete")
                    }
                  </p>
                  
                  <a 
                    href={module.href}
                    className="text-blue-600 dark:text-blue-400 hover:underline text-sm"
                  >
                    {isArabic ? "تعديل" : "Edit"} →
                  </a>
                </div>
              );
            })}
          </div>
        </div>

        {/* Export Section */}
        {hasAnyData && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                {isArabic ? "تصدير السياق الكامل" : "Export Complete Context"}
              </h2>
              
              {/* Format Selector */}
              <div className={`flex items-center justify-between mb-6 ${isArabic ? 'flex-row-reverse' : ''}`}>
                <div className={`flex gap-3 ${isArabic ? 'space-x-reverse space-x-3' : 'space-x-3'}`}>
                  {(['markdown', 'html', 'json'] as const).map((format) => (
                    <button
                      key={format}
                      onClick={() => setOutputFormat(format)}
                      className={`group relative px-6 py-3 text-sm font-medium rounded-xl transition-all duration-300 ease-in-out backdrop-blur-md border overflow-hidden ${
                        outputFormat === format
                          ? 'border-blue-400/50 bg-gradient-to-br from-blue-500/80 via-indigo-500/80 to-purple-600/80 text-white shadow-lg shadow-blue-500/25 scale-105'
                          : 'border-gray-300/50 dark:border-gray-600/50 bg-white/60 dark:bg-gray-800/60 text-gray-700 dark:text-gray-300 hover:bg-white/80 dark:hover:bg-gray-700/80 hover:border-blue-300/50 dark:hover:border-blue-500/50 hover:scale-105 hover:shadow-md'
                      }`}
                    >
                      {/* تأثير الإضاءة */}
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                      {/* المحتوى */}
                      <span className="relative font-arabic">
                        {format.toUpperCase()}
                      </span>

                      {/* تأثير الشرارات للزر النشط */}
                      {outputFormat === format && (
                        <div className="absolute top-0 left-0 w-full h-full">
                          <div className="absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping"></div>
                          <div className="absolute top-2 right-3 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.2s'}}></div>
                          <div className="absolute bottom-1 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.4s'}}></div>
                        </div>
                      )}
                    </button>
                  ))}
                </div>

                <div className={`flex gap-3 ${isArabic ? 'space-x-reverse space-x-3' : 'space-x-3'}`}>
                  <button
                    onClick={handleCopyAll}
                    className="group relative flex items-center gap-3 px-6 py-3 text-sm font-medium rounded-xl transition-all duration-300 ease-in-out backdrop-blur-md border border-green-400/50 overflow-hidden bg-gradient-to-br from-green-500/80 via-emerald-500/80 to-teal-600/80 hover:from-green-600/90 hover:via-emerald-600/90 hover:to-teal-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-green-500/25 hover:scale-105 active:scale-95"
                  >
                    {/* تأثير الإضاءة */}
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* المحتوى */}
                    <div className={`relative flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
                      <span className={`text-lg ${copied ? 'animate-bounce' : 'group-hover:scale-110 transition-transform duration-300'}`}>📋</span>
                      <span className="font-arabic">
                        {copied
                          ? (isArabic ? 'تم النسخ!' : 'Copied!')
                          : (isArabic ? 'نسخ الكل' : 'Copy All')
                        }
                      </span>
                    </div>

                    {/* تأثير الشرارات */}
                    <div className="absolute top-0 left-0 w-full h-full">
                      <div className="absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping"></div>
                      <div className="absolute top-2 right-3 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.2s'}}></div>
                      <div className="absolute bottom-1 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.4s'}}></div>
                    </div>
                  </button>

                  <button
                    onClick={handleDownload}
                    className="group relative flex items-center gap-3 px-6 py-3 text-sm font-medium rounded-xl transition-all duration-300 ease-in-out backdrop-blur-md border border-blue-400/50 overflow-hidden bg-gradient-to-br from-blue-500/80 via-indigo-500/80 to-purple-600/80 hover:from-blue-600/90 hover:via-indigo-600/90 hover:to-purple-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-blue-500/25 hover:scale-105 active:scale-95"
                  >
                    {/* تأثير الإضاءة */}
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* المحتوى */}
                    <div className={`relative flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
                      <span className="text-lg group-hover:scale-110 transition-transform duration-300">💾</span>
                      <span className="font-arabic">
                        {isArabic ? 'تحميل' : 'Download'}
                      </span>
                    </div>

                    {/* تأثير الشرارات */}
                    <div className="absolute top-0 left-0 w-full h-full">
                      <div className="absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping"></div>
                      <div className="absolute top-2 right-3 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.2s'}}></div>
                      <div className="absolute bottom-1 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.4s'}}></div>
                    </div>
                  </button>
                </div>
              </div>

              {/* Preview */}
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 max-h-96 overflow-y-auto">
                <pre className={`text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono ${
                  isArabic ? 'text-right' : 'text-left'
                }`}>
                  {generateCompleteOutput()}
                </pre>
              </div>
            </div>
          </div>
        )}

        {!hasAnyData && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12">
              <span className="text-6xl mb-4 block">📝</span>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                {isArabic ? "لا توجد بيانات للمعاينة" : "No Data to Preview"}
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {isArabic 
                  ? "ابدأ بملء المحاور المختلفة لرؤية المعاينة النهائية"
                  : "Start filling out the different modules to see the final preview"
                }
              </p>
              <a 
                href="/project-definition"
                className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                {isArabic ? "ابدأ من تعريف المشروع" : "Start with Project Definition"}
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
