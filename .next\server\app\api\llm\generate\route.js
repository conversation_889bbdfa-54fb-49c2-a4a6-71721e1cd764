"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/llm/generate/route";
exports.ids = ["app/api/llm/generate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/llm/generate/route.ts */ \"(rsc)/./src/app/api/llm/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/llm/generate/route\",\n        pathname: \"/api/llm/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/llm/generate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\api\\\\llm\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/llm/generate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/llm/generate/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/llm/generate/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/llmProviders */ \"(rsc)/./src/lib/llmProviders.ts\");\n\n\n/**\n * API للتوليد الذكي باستخدام مقدمي خدمات LLM المختلفين\n */ async function POST(request) {\n    try {\n        const { providerId, apiKey, model, messages, context, fieldName, language = \"ar\", temperature = 0.7, maxTokens = 1000, baseUrl } = await request.json();\n        if (!providerId || !apiKey || !messages) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider ID, API key, and messages are required\"\n            }, {\n                status: 400\n            });\n        }\n        const provider = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderById)(providerId);\n        if (!provider) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unknown provider\"\n            }, {\n                status: 400\n            });\n        }\n        const finalBaseUrl = baseUrl || provider.baseUrl;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...(0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderHeaders)(providerId)\n        };\n        // إنشاء system prompt ذكي بناءً على السياق\n        const systemPrompt = createSmartSystemPrompt(context, fieldName, language);\n        const finalMessages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            ...messages\n        ];\n        let response;\n        switch(providerId){\n            case \"openai\":\n            case \"openrouter\":\n            case \"deepseek\":\n            case \"groq\":\n            case \"mistral\":\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"anthropic\":\n                response = await generateAnthropic(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"google\":\n                response = await generateGoogle(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"cohere\":\n                response = await generateCohere(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            default:\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Generation error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createSmartSystemPrompt(context, fieldName, language) {\n    const isArabic = language === \"ar\";\n    const basePrompt = isArabic ? `أنت مساعد ذكي متخصص في مساعدة المستخدمين في بناء سياق منظم ومفصل للمشاريع التقنية والإبداعية.` : `You are an AI assistant specialized in helping users build structured and detailed context for technical and creative projects.`;\n    // تقليل حجم السياق - أخذ المعلومات المهمة فقط\n    const relevantContext = extractRelevantContext(context, fieldName);\n    const contextInfo = isArabic ? `معلومات المشروع: ${relevantContext}` : `Project info: ${relevantContext}`;\n    const fieldGuidance = getFieldGuidance(fieldName, isArabic);\n    const instructions = isArabic ? `\nتعليمات:\n1. قدم إجابة مختصرة ومفيدة (2-3 جمل)\n2. استخدم المعلومات المتوفرة لتحسين إجابتك\n3. اجعل الإجابة عملية وقابلة للتطبيق\n4. استخدم اللغة العربية الواضحة\n` : `\nInstructions:\n1. Provide a concise and helpful response (2-3 sentences)\n2. Use available information to improve your answer\n3. Make the response practical and actionable\n4. Use clear language\n`;\n    return `${basePrompt}\\n\\n${contextInfo}\\n\\n${fieldGuidance}\\n\\n${instructions}`;\n}\nfunction extractRelevantContext(context, fieldName) {\n    if (!context) return \"\";\n    const contextParts = [];\n    // تحليل المرحلة الحالية من المشروع\n    const projectStage = analyzeProjectStage(context);\n    contextParts.push(`Project Stage: ${projectStage}`);\n    // معلومات المشروع الأساسية\n    if (context?.projectDefinition) {\n        const pd = context.projectDefinition;\n        if (pd.name) contextParts.push(`Name: ${pd.name}`);\n        if (pd.purpose) contextParts.push(`Purpose: ${pd.purpose.substring(0, 120)}...`);\n        if (pd.targetUsers) contextParts.push(`Users: ${pd.targetUsers.substring(0, 80)}...`);\n        if (pd.goals) contextParts.push(`Goals: ${pd.goals.substring(0, 80)}...`);\n    }\n    // سياق تقني إذا كان الحقل تقنياً\n    if (isFieldTechnical(fieldName) && context?.technicalLayer) {\n        const tl = context.technicalLayer;\n        if (tl.programmingLanguages) contextParts.push(`Tech: ${tl.programmingLanguages.substring(0, 60)}...`);\n        if (tl.frameworks) contextParts.push(`Frameworks: ${tl.frameworks.substring(0, 60)}...`);\n    }\n    // سياق عاطفي إذا كان الحقل متعلقاً بالتجربة\n    if (isFieldEmotional(fieldName) && context?.emotionalTone) {\n        const et = context.emotionalTone;\n        if (et.personality) contextParts.push(`Personality: ${et.personality.substring(0, 60)}...`);\n        if (et.communicationStyle) contextParts.push(`Style: ${et.communicationStyle.substring(0, 60)}...`);\n    }\n    return contextParts.length > 0 ? contextParts.join(\" | \") : \"New Project\";\n}\nfunction analyzeProjectStage(context) {\n    let completedSections = 0;\n    let totalSections = 5;\n    if (context?.projectDefinition && Object.values(context.projectDefinition).some((v)=>v)) completedSections++;\n    if (context?.contextMap && Object.values(context.contextMap).some((v)=>v)) completedSections++;\n    if (context?.emotionalTone && Object.values(context.emotionalTone).some((v)=>v)) completedSections++;\n    if (context?.technicalLayer && Object.values(context.technicalLayer).some((v)=>v)) completedSections++;\n    if (context?.legalRisk && Object.values(context.legalRisk).some((v)=>v)) completedSections++;\n    const percentage = completedSections / totalSections * 100;\n    if (percentage < 20) return \"Initial Planning\";\n    else if (percentage < 50) return \"Context Building\";\n    else if (percentage < 80) return \"Detail Definition\";\n    else return \"Final Review\";\n}\nfunction isFieldTechnical(fieldName) {\n    return [\n        \"programmingLanguages\",\n        \"frameworks\",\n        \"llmModels\",\n        \"databases\",\n        \"apis\",\n        \"infrastructure\"\n    ].includes(fieldName);\n}\nfunction isFieldEmotional(fieldName) {\n    return [\n        \"personality\",\n        \"communicationStyle\",\n        \"userExperience\",\n        \"brandVoice\",\n        \"emotionalIntelligence\",\n        \"interactionFlow\"\n    ].includes(fieldName);\n}\nfunction getFieldGuidance(fieldName, isArabic) {\n    const fieldGuidanceMap = {\n        name: {\n            ar: \"المجال المطلوب: اسم المشروع - قدم اقتراحات لأسماء إبداعية ومناسبة للمشروع\",\n            en: \"Required field: Project name - Provide suggestions for creative and suitable project names\"\n        },\n        purpose: {\n            ar: \"المجال المطلوب: الغرض من المشروع - اشرح الهدف الرئيسي والقيمة المضافة\",\n            en: \"Required field: Project purpose - Explain the main goal and added value\"\n        },\n        targetUsers: {\n            ar: \"المجال المطلوب: المستخدمون المستهدفون - حدد الجمهور المستهدف بدقة\",\n            en: \"Required field: Target users - Define the target audience precisely\"\n        },\n        goals: {\n            ar: \"المجال المطلوب: الأهداف - حدد أهداف واضحة وقابلة للقياس\",\n            en: \"Required field: Goals - Define clear and measurable objectives\"\n        },\n        scope: {\n            ar: \"المجال المطلوب: نطاق المشروع - حدد حدود وإمكانيات المشروع\",\n            en: \"Required field: Project scope - Define project boundaries and capabilities\"\n        },\n        timeline: {\n            ar: \"المجال المطلوب: الجدول الزمني - اقترح خطة زمنية واقعية\",\n            en: \"Required field: Timeline - Suggest a realistic time plan\"\n        },\n        programmingLanguages: {\n            ar: \"المجال المطلوب: لغات البرمجة - اقترح أفضل لغات البرمجة للمشروع\",\n            en: \"Required field: Programming languages - Suggest the best programming languages for the project\"\n        },\n        frameworks: {\n            ar: \"المجال المطلوب: الأطر التقنية - اقترح أفضل الأطر والمكتبات\",\n            en: \"Required field: Frameworks - Suggest the best frameworks and libraries\"\n        },\n        databases: {\n            ar: \"المجال المطلوب: قواعد البيانات - اقترح أنسب قواعد البيانات\",\n            en: \"Required field: Databases - Suggest the most suitable databases\"\n        }\n    };\n    const guidance = fieldGuidanceMap[fieldName];\n    if (guidance) {\n        return isArabic ? guidance.ar : guidance.en;\n    }\n    return isArabic ? `المجال المطلوب: ${fieldName} - قدم محتوى مفيد ومناسب لهذا المجال` : `Required field: ${fieldName} - Provide helpful and appropriate content for this field`;\n}\nasync function generateOpenAICompatible(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout للطلب\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30 ثانية timeout\n        const response = await fetch(`${baseUrl}/chat/completions`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model,\n                messages,\n                temperature,\n                max_tokens: Math.min(maxTokens, 300),\n                stream: false // تأكد من عدم استخدام streaming\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.choices?.[0]?.message?.content || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateAnthropic(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // Convert OpenAI format to Anthropic format\n        const anthropicMessages = messages.filter((m)=>m.role !== \"system\");\n        const systemMessage = messages.find((m)=>m.role === \"system\")?.content || \"\";\n        const response = await fetch(`${baseUrl}/messages`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"x-api-key\": apiKey\n            },\n            body: JSON.stringify({\n                model,\n                max_tokens: maxTokens,\n                temperature,\n                system: systemMessage,\n                messages: anthropicMessages\n            })\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.content?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateGoogle(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000);\n        // Convert to Google format\n        const contents = messages.map((msg)=>({\n                role: msg.role === \"assistant\" ? \"model\" : \"user\",\n                parts: [\n                    {\n                        text: msg.content\n                    }\n                ]\n            }));\n        const response = await fetch(`${baseUrl}/models/${model}:generateContent?key=${apiKey}`, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify({\n                contents,\n                generationConfig: {\n                    temperature,\n                    maxOutputTokens: Math.min(maxTokens, 300),\n                    candidateCount: 1 // طلب مرشح واحد فقط للسرعة\n                }\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.candidates?.[0]?.content?.parts?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usageMetadata\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateCohere(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout للطلب\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000);\n        // تحويل messages إلى تنسيق Cohere\n        const lastMessage = messages[messages.length - 1];\n        const chatHistory = messages.slice(0, -1).map((msg)=>({\n                role: msg.role === \"assistant\" ? \"CHATBOT\" : \"USER\",\n                message: msg.content\n            }));\n        const response = await fetch(`${baseUrl}/chat`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model,\n                message: lastMessage.content,\n                chat_history: chatHistory,\n                temperature,\n                max_tokens: Math.min(maxTokens, 300),\n                stream: false,\n                connectors: []\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: {\n                input_tokens: data.meta?.tokens?.input_tokens || 0,\n                output_tokens: data.meta?.tokens?.output_tokens || 0\n            }\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Cohere generation failed\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/llm/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model with vision capabilities\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable version of GPT-4o\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model with latest knowledge\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-4\",\n                name: \"GPT-4\",\n                description: \"Original GPT-4 model with strong reasoning\",\n                contextLength: 8192,\n                pricing: \"$30/1M input, $60/1M output\",\n                inputPrice: 30,\n                outputPrice: 60\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient for most tasks\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gpt-3.5-turbo-instruct\",\n                name: \"GPT-3.5 Turbo Instruct\",\n                description: \"Instruction-following variant\",\n                contextLength: 4096,\n                pricing: \"$1.5/1M input, $2/1M output\",\n                inputPrice: 1.5,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Most advanced model with 2M context window\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient with 1M context\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-1.5-flash-8b\",\n                name: \"Gemini 1.5 Flash 8B\",\n                description: \"Smaller, faster model for simple tasks\",\n                contextLength: 1000000,\n                pricing: \"$0.0375/1M input, $0.15/1M output\",\n                inputPrice: 0.0375,\n                outputPrice: 0.15\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance for general use\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gemini-pro-vision\",\n                name: \"Gemini Pro Vision\",\n                description: \"Multimodal model with vision capabilities\",\n                contextLength: 16000,\n                pricing: \"$0.25/1M input, $0.5/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"mistral\",\n        name: \"Mistral AI\",\n        icon: \"\\uD83C\\uDF1F\",\n        description: \"Mistral AI - Advanced European AI models with multilingual capabilities\",\n        baseUrl: \"https://api.mistral.ai/v1\",\n        apiKeyPlaceholder: \"mistral_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"mistral-large-latest\",\n                name: \"Mistral Large\",\n                description: \"Most advanced model with superior reasoning\",\n                contextLength: 128000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-medium-latest\",\n                name: \"Mistral Medium\",\n                description: \"Balanced performance and cost\",\n                contextLength: 32000,\n                pricing: \"$2.7/1M input, $8.1/1M output\",\n                inputPrice: 2.7,\n                outputPrice: 8.1\n            },\n            {\n                id: \"mistral-small-latest\",\n                name: \"Mistral Small\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 32000,\n                pricing: \"$0.2/1M input, $0.6/1M output\",\n                inputPrice: 0.2,\n                outputPrice: 0.6\n            },\n            {\n                id: \"open-mistral-7b\",\n                name: \"Open Mistral 7B\",\n                description: \"Open source model, fast and efficient\",\n                contextLength: 32000,\n                pricing: \"$0.25/1M input, $0.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.25\n            },\n            {\n                id: \"open-mixtral-8x7b\",\n                name: \"Open Mixtral 8x7B\",\n                description: \"Mixture of experts model\",\n                contextLength: 32000,\n                pricing: \"$0.7/1M input, $0.7/1M output\",\n                inputPrice: 0.7,\n                outputPrice: 0.7\n            },\n            {\n                id: \"open-mixtral-8x22b\",\n                name: \"Open Mixtral 8x22B\",\n                description: \"Larger mixture of experts model\",\n                contextLength: 64000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-embed\",\n                name: \"Mistral Embed\",\n                description: \"Embedding model for semantic search\",\n                contextLength: 8192,\n                pricing: \"$0.1/1M tokens\",\n                inputPrice: 0.1,\n                outputPrice: 0\n            }\n        ]\n    },\n    {\n        id: \"cohere\",\n        name: \"Cohere\",\n        icon: \"\\uD83E\\uDDEE\",\n        description: \"Cohere - Enterprise-grade language models with strong multilingual support\",\n        baseUrl: \"https://api.cohere.ai/v1\",\n        apiKeyPlaceholder: \"co_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"command-r-plus\",\n                name: \"Command R+\",\n                description: \"Most advanced model for complex reasoning and RAG\",\n                contextLength: 128000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"command-r\",\n                name: \"Command R\",\n                description: \"Balanced model for general use and RAG\",\n                contextLength: 128000,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"command\",\n                name: \"Command\",\n                description: \"Versatile model for various tasks\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            },\n            {\n                id: \"command-light\",\n                name: \"Command Light\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 4096,\n                pricing: \"$0.3/1M input, $0.6/1M output\",\n                inputPrice: 0.3,\n                outputPrice: 0.6\n            },\n            {\n                id: \"command-nightly\",\n                name: \"Command Nightly\",\n                description: \"Latest experimental features\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    const baseHeaders = provider?.headers || {};\n    // إضافة headers خاصة لكل مزود\n    switch(providerId){\n        case \"anthropic\":\n            return {\n                ...baseHeaders,\n                \"anthropic-version\": \"2023-06-01\"\n            };\n        case \"cohere\":\n            return {\n                ...baseHeaders,\n                \"Cohere-Version\": \"2022-12-06\"\n            };\n        case \"mistral\":\n            return {\n                ...baseHeaders,\n                \"User-Agent\": \"ContextKit/1.0\"\n            };\n        default:\n            return baseHeaders;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2xsbVByb3ZpZGVycy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRUE7OztDQUdDLEdBRU0sTUFBTUEseUJBQXdDO0lBQ25EO1FBQ0VDLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxtQkFBbUI7UUFDbkJDLFVBQVU7UUFDVkMsbUJBQW1CO1FBQ25CQyxXQUFXO1FBQ1hDLFFBQVE7WUFDTjtnQkFDRVQsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRWIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7U0FDRDtJQUNIO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLG1CQUFtQjtRQUNuQkMsVUFBVTtRQUNWQyxtQkFBbUI7UUFDbkJDLFdBQVc7UUFDWE0sU0FBUztZQUNQLHFCQUFxQjtRQUN2QjtRQUNBTCxRQUFRO1lBQ047Z0JBQ0VULElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRWIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1NBQ0Q7SUFDSDtJQUNBO1FBQ0ViLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxtQkFBbUI7UUFDbkJDLFVBQVU7UUFDVkMsbUJBQW1CO1FBQ25CQyxXQUFXO1FBQ1hDLFFBQVE7WUFDTjtnQkFDRVQsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRWIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtTQUNEO0lBQ0g7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsbUJBQW1CO1FBQ25CQyxVQUFVO1FBQ1ZDLG1CQUFtQjtRQUNuQkMsV0FBVztRQUNYTSxTQUFTO1lBQ1AsZ0JBQWdCQyxRQUFRQyxHQUFHLENBQUNDLG9CQUFvQixJQUFJO1lBQ3BELFdBQVc7UUFDYjtRQUNBUixRQUFRO1lBQ047Z0JBQ0VULElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7WUFDWDtZQUNBO2dCQUNFWCxJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO1lBQ1g7WUFDQTtnQkFDRVgsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztZQUNYO1lBQ0E7Z0JBQ0VYLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7WUFDWDtTQUNEO0lBQ0g7SUFDQTtRQUNFWCxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsbUJBQW1CO1FBQ25CQyxVQUFVO1FBQ1ZDLG1CQUFtQjtRQUNuQkMsV0FBVztRQUNYQyxRQUFRO1lBQ047Z0JBQ0VULElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7U0FDRDtJQUNIO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLG1CQUFtQjtRQUNuQkMsVUFBVTtRQUNWQyxtQkFBbUI7UUFDbkJDLFdBQVc7UUFDWEMsUUFBUTtZQUNOO2dCQUNFVCxJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRWIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRWIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7U0FDRDtJQUNIO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLG1CQUFtQjtRQUNuQkMsVUFBVTtRQUNWQyxtQkFBbUI7UUFDbkJDLFdBQVc7UUFDWEMsUUFBUTtZQUNOO2dCQUNFVCxJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRWIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRWIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1NBQ0Q7SUFDSDtJQUNBO1FBQ0ViLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxtQkFBbUI7UUFDbkJDLFVBQVU7UUFDVkMsbUJBQW1CO1FBQ25CQyxXQUFXO1FBQ1hDLFFBQVE7WUFDTjtnQkFDRVQsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkUsYUFBYTtnQkFDYk8sZUFBZTtnQkFDZkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQ0ViLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05FLGFBQWE7Z0JBQ2JPLGVBQWU7Z0JBQ2ZDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGFBQWE7WUFDZjtZQUNBO2dCQUNFYixJQUFJO2dCQUNKQyxNQUFNO2dCQUNORSxhQUFhO2dCQUNiTyxlQUFlO2dCQUNmQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxhQUFhO1lBQ2Y7U0FDRDtJQUNIO0NBQ0QsQ0FBQztBQUVGOztDQUVDLEdBQ00sU0FBU0ssZ0JBQWdCbEIsRUFBVTtJQUN4QyxPQUFPRCx1QkFBdUJvQixJQUFJLENBQUNDLENBQUFBLFdBQVlBLFNBQVNwQixFQUFFLEtBQUtBO0FBQ2pFO0FBRUE7O0NBRUMsR0FDTSxTQUFTcUI7SUFDZCxPQUFPdEIsdUJBQXVCdUIsTUFBTSxDQUFDRixDQUFBQSxXQUFZQSxTQUFTZCxRQUFRO0FBQ3BFO0FBRUE7O0NBRUMsR0FDTSxTQUFTaUIsYUFBYUMsVUFBa0IsRUFBRUMsT0FBZTtJQUM5RCxNQUFNTCxXQUFXRixnQkFBZ0JNO0lBQ2pDLE9BQU9KLFVBQVVYLE9BQU9VLEtBQUtPLENBQUFBLFFBQVNBLE1BQU0xQixFQUFFLEtBQUt5QjtBQUNyRDtBQUVBOztDQUVDLEdBQ00sU0FBU0UsZ0JBQWdCQyxLQUFhO0lBQzNDLE1BQU1DLGlCQUFpQkQsTUFBTUUsV0FBVztJQUN4QyxPQUFPL0IsdUJBQXVCdUIsTUFBTSxDQUFDRixDQUFBQSxXQUNuQ0EsU0FBU25CLElBQUksQ0FBQzZCLFdBQVcsR0FBR0MsUUFBUSxDQUFDRixtQkFDckNULFNBQVNqQixXQUFXLENBQUMyQixXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsbUJBQzVDVCxTQUFTWCxNQUFNLENBQUN1QixJQUFJLENBQUNOLENBQUFBLFFBQ25CQSxNQUFNekIsSUFBSSxDQUFDNkIsV0FBVyxHQUFHQyxRQUFRLENBQUNGLG1CQUNsQ0gsTUFBTXZCLFdBQVcsQ0FBQzJCLFdBQVcsR0FBR0MsUUFBUSxDQUFDRjtBQUcvQztBQUVBOztDQUVDLEdBQ00sU0FBU0ksbUJBQW1CVCxVQUFrQjtJQUNuRCxNQUFNSixXQUFXRixnQkFBZ0JNO0lBQ2pDLE9BQU9KLFVBQVVoQixXQUFXO0FBQzlCO0FBRUE7O0NBRUMsR0FDTSxTQUFTOEIsbUJBQW1CVixVQUFrQjtJQUNuRCxNQUFNSixXQUFXRixnQkFBZ0JNO0lBQ2pDLE1BQU1XLGNBQWNmLFVBQVVOLFdBQVcsQ0FBQztJQUUxQyw4QkFBOEI7SUFDOUIsT0FBUVU7UUFDTixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHVyxXQUFXO2dCQUNkLHFCQUFxQjtZQUN2QjtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdBLFdBQVc7Z0JBQ2Qsa0JBQWtCO1lBQ3BCO1FBQ0YsS0FBSztZQUNILE9BQU87Z0JBQ0wsR0FBR0EsV0FBVztnQkFDZCxjQUFjO1lBQ2hCO1FBQ0Y7WUFDRSxPQUFPQTtJQUNYO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Ly4vc3JjL2xpYi9sbG1Qcm92aWRlcnMudHM/ZTYxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMTE1Qcm92aWRlciB9IGZyb20gJ0Avc3RvcmUvY29udGV4dFN0b3JlJztcblxuLyoqXG4gKiDZgtin2LnYr9ipINio2YrYp9mG2KfYqiDYtNin2YXZhNipINmE2YXZgtiv2YXZiiDYrtiv2YXYp9iqIExMTVxuICog2KrYrdiq2YjZiiDYudmE2Ykg2YXYudmE2YjZhdin2Kog2YPYp9mF2YTYqSDYudmGINmD2YQg2YXZgtiv2YUg2K7Yr9mF2Kkg2YXYuSBCYXNlIFVSTHMg2YjYp9mE2YbZhdin2LDYrFxuICovXG5cbmV4cG9ydCBjb25zdCBMTE1fUFJPVklERVJTX0RBVEFCQVNFOiBMTE1Qcm92aWRlcltdID0gW1xuICB7XG4gICAgaWQ6ICdvcGVuYWknLFxuICAgIG5hbWU6ICdPcGVuQUknLFxuICAgIGljb246ICfwn6SWJyxcbiAgICBkZXNjcmlwdGlvbjogJ0dQVCBtb2RlbHMgZnJvbSBPcGVuQUkgLSBJbmR1c3RyeSBsZWFkaW5nIGxhbmd1YWdlIG1vZGVscycsXG4gICAgYmFzZVVybDogJ2h0dHBzOi8vYXBpLm9wZW5haS5jb20vdjEnLFxuICAgIGFwaUtleVBsYWNlaG9sZGVyOiAnc2stLi4uJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICBzdXBwb3J0c1N0cmVhbWluZzogdHJ1ZSxcbiAgICBtYXhUb2tlbnM6IDEyODAwMCxcbiAgICBtb2RlbHM6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdncHQtNG8nLFxuICAgICAgICBuYW1lOiAnR1BULTRvJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdNb3N0IGFkdmFuY2VkIG11bHRpbW9kYWwgbW9kZWwgd2l0aCB2aXNpb24gY2FwYWJpbGl0aWVzJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMTI4MDAwLFxuICAgICAgICBwcmljaW5nOiAnJDUvMU0gaW5wdXQsICQxNS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiA1LFxuICAgICAgICBvdXRwdXRQcmljZTogMTVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnZ3B0LTRvLW1pbmknLFxuICAgICAgICBuYW1lOiAnR1BULTRvIE1pbmknLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0Zhc3RlciBhbmQgbW9yZSBhZmZvcmRhYmxlIHZlcnNpb24gb2YgR1BULTRvJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMTI4MDAwLFxuICAgICAgICBwcmljaW5nOiAnJDAuMTUvMU0gaW5wdXQsICQwLjYvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMC4xNSxcbiAgICAgICAgb3V0cHV0UHJpY2U6IDAuNlxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdncHQtNC10dXJibycsXG4gICAgICAgIG5hbWU6ICdHUFQtNCBUdXJibycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnSGlnaCBwZXJmb3JtYW5jZSBtb2RlbCB3aXRoIGxhdGVzdCBrbm93bGVkZ2UnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiAxMjgwMDAsXG4gICAgICAgIHByaWNpbmc6ICckMTAvMU0gaW5wdXQsICQzMC8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAxMCxcbiAgICAgICAgb3V0cHV0UHJpY2U6IDMwXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ2dwdC00JyxcbiAgICAgICAgbmFtZTogJ0dQVC00JyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdPcmlnaW5hbCBHUFQtNCBtb2RlbCB3aXRoIHN0cm9uZyByZWFzb25pbmcnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiA4MTkyLFxuICAgICAgICBwcmljaW5nOiAnJDMwLzFNIGlucHV0LCAkNjAvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMzAsXG4gICAgICAgIG91dHB1dFByaWNlOiA2MFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdncHQtMy41LXR1cmJvJyxcbiAgICAgICAgbmFtZTogJ0dQVC0zLjUgVHVyYm8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0Zhc3QgYW5kIGVmZmljaWVudCBmb3IgbW9zdCB0YXNrcycsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDE2Mzg1LFxuICAgICAgICBwcmljaW5nOiAnJDAuNS8xTSBpbnB1dCwgJDEuNS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjUsXG4gICAgICAgIG91dHB1dFByaWNlOiAxLjVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnZ3B0LTMuNS10dXJiby1pbnN0cnVjdCcsXG4gICAgICAgIG5hbWU6ICdHUFQtMy41IFR1cmJvIEluc3RydWN0JyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdJbnN0cnVjdGlvbi1mb2xsb3dpbmcgdmFyaWFudCcsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDQwOTYsXG4gICAgICAgIHByaWNpbmc6ICckMS41LzFNIGlucHV0LCAkMi8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAxLjUsXG4gICAgICAgIG91dHB1dFByaWNlOiAyXG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICdhbnRocm9waWMnLFxuICAgIG5hbWU6ICdBbnRocm9waWMnLFxuICAgIGljb246ICfwn6egJyxcbiAgICBkZXNjcmlwdGlvbjogJ0NsYXVkZSBtb2RlbHMgZnJvbSBBbnRocm9waWMgLSBBZHZhbmNlZCByZWFzb25pbmcgY2FwYWJpbGl0aWVzJyxcbiAgICBiYXNlVXJsOiAnaHR0cHM6Ly9hcGkuYW50aHJvcGljLmNvbS92MScsXG4gICAgYXBpS2V5UGxhY2Vob2xkZXI6ICdzay1hbnQtLi4uJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICBzdXBwb3J0c1N0cmVhbWluZzogdHJ1ZSxcbiAgICBtYXhUb2tlbnM6IDIwMDAwMCxcbiAgICBoZWFkZXJzOiB7XG4gICAgICAnYW50aHJvcGljLXZlcnNpb24nOiAnMjAyMy0wNi0wMSdcbiAgICB9LFxuICAgIG1vZGVsczogW1xuICAgICAge1xuICAgICAgICBpZDogJ2NsYXVkZS0zLTUtc29ubmV0LTIwMjQxMDIyJyxcbiAgICAgICAgbmFtZTogJ0NsYXVkZSAzLjUgU29ubmV0JyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdNb3N0IGludGVsbGlnZW50IG1vZGVsJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMjAwMDAwLFxuICAgICAgICBwcmljaW5nOiAnJDMvMU0gaW5wdXQsICQxNS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAzLFxuICAgICAgICBvdXRwdXRQcmljZTogMTVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnY2xhdWRlLTMtNS1oYWlrdS0yMDI0MTAyMicsXG4gICAgICAgIG5hbWU6ICdDbGF1ZGUgMy41IEhhaWt1JyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdGYXN0ZXN0IG1vZGVsJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMjAwMDAwLFxuICAgICAgICBwcmljaW5nOiAnJDAuMjUvMU0gaW5wdXQsICQxLjI1LzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDAuMjUsXG4gICAgICAgIG91dHB1dFByaWNlOiAxLjI1XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ2NsYXVkZS0zLW9wdXMtMjAyNDAyMjknLFxuICAgICAgICBuYW1lOiAnQ2xhdWRlIDMgT3B1cycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTW9zdCBwb3dlcmZ1bCBtb2RlbCcsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDIwMDAwMCxcbiAgICAgICAgcHJpY2luZzogJyQxNS8xTSBpbnB1dCwgJDc1LzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDE1LFxuICAgICAgICBvdXRwdXRQcmljZTogNzVcbiAgICAgIH1cbiAgICBdXG4gIH0sXG4gIHtcbiAgICBpZDogJ2dvb2dsZScsXG4gICAgbmFtZTogJ0dvb2dsZSBBSScsXG4gICAgaWNvbjogJ/CflI0nLFxuICAgIGRlc2NyaXB0aW9uOiAnR2VtaW5pIG1vZGVscyBmcm9tIEdvb2dsZSAtIE11bHRpbW9kYWwgQUkgY2FwYWJpbGl0aWVzJyxcbiAgICBiYXNlVXJsOiAnaHR0cHM6Ly9nZW5lcmF0aXZlbGFuZ3VhZ2UuZ29vZ2xlYXBpcy5jb20vdjFiZXRhJyxcbiAgICBhcGlLZXlQbGFjZWhvbGRlcjogJ0FJemEuLi4nLFxuICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgIHN1cHBvcnRzU3RyZWFtaW5nOiB0cnVlLFxuICAgIG1heFRva2VuczogMjAwMDAwMCxcbiAgICBtb2RlbHM6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdnZW1pbmktMS41LXBybycsXG4gICAgICAgIG5hbWU6ICdHZW1pbmkgMS41IFBybycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTW9zdCBhZHZhbmNlZCBtb2RlbCB3aXRoIDJNIGNvbnRleHQgd2luZG93JyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMjAwMDAwMCxcbiAgICAgICAgcHJpY2luZzogJyQxLjI1LzFNIGlucHV0LCAkNS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAxLjI1LFxuICAgICAgICBvdXRwdXRQcmljZTogNVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdnZW1pbmktMS41LWZsYXNoJyxcbiAgICAgICAgbmFtZTogJ0dlbWluaSAxLjUgRmxhc2gnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0Zhc3QgYW5kIGVmZmljaWVudCB3aXRoIDFNIGNvbnRleHQnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiAxMDAwMDAwLFxuICAgICAgICBwcmljaW5nOiAnJDAuMDc1LzFNIGlucHV0LCAkMC4zLzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDAuMDc1LFxuICAgICAgICBvdXRwdXRQcmljZTogMC4zXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ2dlbWluaS0xLjUtZmxhc2gtOGInLFxuICAgICAgICBuYW1lOiAnR2VtaW5pIDEuNSBGbGFzaCA4QicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnU21hbGxlciwgZmFzdGVyIG1vZGVsIGZvciBzaW1wbGUgdGFza3MnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiAxMDAwMDAwLFxuICAgICAgICBwcmljaW5nOiAnJDAuMDM3NS8xTSBpbnB1dCwgJDAuMTUvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMC4wMzc1LFxuICAgICAgICBvdXRwdXRQcmljZTogMC4xNVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdnZW1pbmktcHJvJyxcbiAgICAgICAgbmFtZTogJ0dlbWluaSBQcm8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0JhbGFuY2VkIHBlcmZvcm1hbmNlIGZvciBnZW5lcmFsIHVzZScsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDMyNzY4LFxuICAgICAgICBwcmljaW5nOiAnJDAuNS8xTSBpbnB1dCwgJDEuNS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjUsXG4gICAgICAgIG91dHB1dFByaWNlOiAxLjVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnZ2VtaW5pLXByby12aXNpb24nLFxuICAgICAgICBuYW1lOiAnR2VtaW5pIFBybyBWaXNpb24nLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ011bHRpbW9kYWwgbW9kZWwgd2l0aCB2aXNpb24gY2FwYWJpbGl0aWVzJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMTYwMDAsXG4gICAgICAgIHByaWNpbmc6ICckMC4yNS8xTSBpbnB1dCwgJDAuNS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjI1LFxuICAgICAgICBvdXRwdXRQcmljZTogMC41XG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICdvcGVucm91dGVyJyxcbiAgICBuYW1lOiAnT3BlblJvdXRlcicsXG4gICAgaWNvbjogJ/CflIAnLFxuICAgIGRlc2NyaXB0aW9uOiAnQWNjZXNzIHRvIG11bHRpcGxlIG1vZGVscyB2aWEgT3BlblJvdXRlciAtIE9uZSBBUEkgZm9yIGFsbCBtb2RlbHMnLFxuICAgIGJhc2VVcmw6ICdodHRwczovL29wZW5yb3V0ZXIuYWkvYXBpL3YxJyxcbiAgICBhcGlLZXlQbGFjZWhvbGRlcjogJ3NrLW9yLS4uLicsXG4gICAgaXNBY3RpdmU6IHRydWUsXG4gICAgc3VwcG9ydHNTdHJlYW1pbmc6IHRydWUsXG4gICAgbWF4VG9rZW5zOiAyMDAwMDAsXG4gICAgaGVhZGVyczoge1xuICAgICAgJ0hUVFAtUmVmZXJlcic6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NJVEVfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAnLFxuICAgICAgJ1gtVGl0bGUnOiAnQ29udGV4dEtpdCdcbiAgICB9LFxuICAgIG1vZGVsczogW1xuICAgICAge1xuICAgICAgICBpZDogJ29wZW5haS9ncHQtNG8nLFxuICAgICAgICBuYW1lOiAnR1BULTRvICh2aWEgT3BlblJvdXRlciknLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ09wZW5BSSBHUFQtNG8gdGhyb3VnaCBPcGVuUm91dGVyJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMTI4MDAwLFxuICAgICAgICBwcmljaW5nOiAnVmFyaWFibGUgcHJpY2luZydcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnYW50aHJvcGljL2NsYXVkZS0zLjUtc29ubmV0JyxcbiAgICAgICAgbmFtZTogJ0NsYXVkZSAzLjUgU29ubmV0ICh2aWEgT3BlblJvdXRlciknLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0FudGhyb3BpYyBDbGF1ZGUgdGhyb3VnaCBPcGVuUm91dGVyJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMjAwMDAwLFxuICAgICAgICBwcmljaW5nOiAnVmFyaWFibGUgcHJpY2luZydcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnZ29vZ2xlL2dlbWluaS1wcm8tMS41JyxcbiAgICAgICAgbmFtZTogJ0dlbWluaSBQcm8gMS41ICh2aWEgT3BlblJvdXRlciknLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0dvb2dsZSBHZW1pbmkgdGhyb3VnaCBPcGVuUm91dGVyJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMTAwMDAwMCxcbiAgICAgICAgcHJpY2luZzogJ1ZhcmlhYmxlIHByaWNpbmcnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ21ldGEtbGxhbWEvbGxhbWEtMy4xLTQwNWItaW5zdHJ1Y3QnLFxuICAgICAgICBuYW1lOiAnTGxhbWEgMy4xIDQwNUIgKHZpYSBPcGVuUm91dGVyKScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTWV0YSBMbGFtYSB0aHJvdWdoIE9wZW5Sb3V0ZXInLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiAxMzEwNzIsXG4gICAgICAgIHByaWNpbmc6ICdWYXJpYWJsZSBwcmljaW5nJ1xuICAgICAgfVxuICAgIF1cbiAgfSxcbiAge1xuICAgIGlkOiAnZGVlcHNlZWsnLFxuICAgIG5hbWU6ICdEZWVwU2VlaycsXG4gICAgaWNvbjogJ/CfjIonLFxuICAgIGRlc2NyaXB0aW9uOiAnRGVlcFNlZWsgbW9kZWxzIC0gRWZmaWNpZW50IGFuZCBjb3N0LWVmZmVjdGl2ZSBBSScsXG4gICAgYmFzZVVybDogJ2h0dHBzOi8vYXBpLmRlZXBzZWVrLmNvbS92MScsXG4gICAgYXBpS2V5UGxhY2Vob2xkZXI6ICdzay0uLi4nLFxuICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgIHN1cHBvcnRzU3RyZWFtaW5nOiB0cnVlLFxuICAgIG1heFRva2VuczogMzI3NjgsXG4gICAgbW9kZWxzOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnZGVlcHNlZWstY2hhdCcsXG4gICAgICAgIG5hbWU6ICdEZWVwU2VlayBDaGF0JyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdHZW5lcmFsIHB1cnBvc2UgY29udmVyc2F0aW9uYWwgQUknLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiAzMjc2OCxcbiAgICAgICAgcHJpY2luZzogJyQwLjE0LzFNIGlucHV0LCAkMC4yOC8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjE0LFxuICAgICAgICBvdXRwdXRQcmljZTogMC4yOFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdkZWVwc2Vlay1jb2RlcicsXG4gICAgICAgIG5hbWU6ICdEZWVwU2VlayBDb2RlcicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnU3BlY2lhbGl6ZWQgZm9yIGNvZGUgZ2VuZXJhdGlvbicsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDE2Mzg0LFxuICAgICAgICBwcmljaW5nOiAnJDAuMTQvMU0gaW5wdXQsICQwLjI4LzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDAuMTQsXG4gICAgICAgIG91dHB1dFByaWNlOiAwLjI4XG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICdtaXN0cmFsJyxcbiAgICBuYW1lOiAnTWlzdHJhbCBBSScsXG4gICAgaWNvbjogJ/CfjJ8nLFxuICAgIGRlc2NyaXB0aW9uOiAnTWlzdHJhbCBBSSAtIEFkdmFuY2VkIEV1cm9wZWFuIEFJIG1vZGVscyB3aXRoIG11bHRpbGluZ3VhbCBjYXBhYmlsaXRpZXMnLFxuICAgIGJhc2VVcmw6ICdodHRwczovL2FwaS5taXN0cmFsLmFpL3YxJyxcbiAgICBhcGlLZXlQbGFjZWhvbGRlcjogJ21pc3RyYWxfLi4uJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICBzdXBwb3J0c1N0cmVhbWluZzogdHJ1ZSxcbiAgICBtYXhUb2tlbnM6IDEyODAwMCxcbiAgICBtb2RlbHM6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdtaXN0cmFsLWxhcmdlLWxhdGVzdCcsXG4gICAgICAgIG5hbWU6ICdNaXN0cmFsIExhcmdlJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdNb3N0IGFkdmFuY2VkIG1vZGVsIHdpdGggc3VwZXJpb3IgcmVhc29uaW5nJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMTI4MDAwLFxuICAgICAgICBwcmljaW5nOiAnJDIvMU0gaW5wdXQsICQ2LzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDIsXG4gICAgICAgIG91dHB1dFByaWNlOiA2XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ21pc3RyYWwtbWVkaXVtLWxhdGVzdCcsXG4gICAgICAgIG5hbWU6ICdNaXN0cmFsIE1lZGl1bScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQmFsYW5jZWQgcGVyZm9ybWFuY2UgYW5kIGNvc3QnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiAzMjAwMCxcbiAgICAgICAgcHJpY2luZzogJyQyLjcvMU0gaW5wdXQsICQ4LjEvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMi43LFxuICAgICAgICBvdXRwdXRQcmljZTogOC4xXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ21pc3RyYWwtc21hbGwtbGF0ZXN0JyxcbiAgICAgICAgbmFtZTogJ01pc3RyYWwgU21hbGwnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0Zhc3QgYW5kIGVmZmljaWVudCBmb3Igc2ltcGxlIHRhc2tzJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMzIwMDAsXG4gICAgICAgIHByaWNpbmc6ICckMC4yLzFNIGlucHV0LCAkMC42LzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDAuMixcbiAgICAgICAgb3V0cHV0UHJpY2U6IDAuNlxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdvcGVuLW1pc3RyYWwtN2InLFxuICAgICAgICBuYW1lOiAnT3BlbiBNaXN0cmFsIDdCJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdPcGVuIHNvdXJjZSBtb2RlbCwgZmFzdCBhbmQgZWZmaWNpZW50JyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMzIwMDAsXG4gICAgICAgIHByaWNpbmc6ICckMC4yNS8xTSBpbnB1dCwgJDAuMjUvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMC4yNSxcbiAgICAgICAgb3V0cHV0UHJpY2U6IDAuMjVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnb3Blbi1taXh0cmFsLTh4N2InLFxuICAgICAgICBuYW1lOiAnT3BlbiBNaXh0cmFsIDh4N0InLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ01peHR1cmUgb2YgZXhwZXJ0cyBtb2RlbCcsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDMyMDAwLFxuICAgICAgICBwcmljaW5nOiAnJDAuNy8xTSBpbnB1dCwgJDAuNy8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjcsXG4gICAgICAgIG91dHB1dFByaWNlOiAwLjdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnb3Blbi1taXh0cmFsLTh4MjJiJyxcbiAgICAgICAgbmFtZTogJ09wZW4gTWl4dHJhbCA4eDIyQicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTGFyZ2VyIG1peHR1cmUgb2YgZXhwZXJ0cyBtb2RlbCcsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDY0MDAwLFxuICAgICAgICBwcmljaW5nOiAnJDIvMU0gaW5wdXQsICQ2LzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDIsXG4gICAgICAgIG91dHB1dFByaWNlOiA2XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ21pc3RyYWwtZW1iZWQnLFxuICAgICAgICBuYW1lOiAnTWlzdHJhbCBFbWJlZCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRW1iZWRkaW5nIG1vZGVsIGZvciBzZW1hbnRpYyBzZWFyY2gnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiA4MTkyLFxuICAgICAgICBwcmljaW5nOiAnJDAuMS8xTSB0b2tlbnMnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjEsXG4gICAgICAgIG91dHB1dFByaWNlOiAwXG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICdjb2hlcmUnLFxuICAgIG5hbWU6ICdDb2hlcmUnLFxuICAgIGljb246ICfwn6euJyxcbiAgICBkZXNjcmlwdGlvbjogJ0NvaGVyZSAtIEVudGVycHJpc2UtZ3JhZGUgbGFuZ3VhZ2UgbW9kZWxzIHdpdGggc3Ryb25nIG11bHRpbGluZ3VhbCBzdXBwb3J0JyxcbiAgICBiYXNlVXJsOiAnaHR0cHM6Ly9hcGkuY29oZXJlLmFpL3YxJyxcbiAgICBhcGlLZXlQbGFjZWhvbGRlcjogJ2NvXy4uLicsXG4gICAgaXNBY3RpdmU6IHRydWUsXG4gICAgc3VwcG9ydHNTdHJlYW1pbmc6IHRydWUsXG4gICAgbWF4VG9rZW5zOiAxMjgwMDAsXG4gICAgbW9kZWxzOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnY29tbWFuZC1yLXBsdXMnLFxuICAgICAgICBuYW1lOiAnQ29tbWFuZCBSKycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTW9zdCBhZHZhbmNlZCBtb2RlbCBmb3IgY29tcGxleCByZWFzb25pbmcgYW5kIFJBRycsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDEyODAwMCxcbiAgICAgICAgcHJpY2luZzogJyQzLzFNIGlucHV0LCAkMTUvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMyxcbiAgICAgICAgb3V0cHV0UHJpY2U6IDE1XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ2NvbW1hbmQtcicsXG4gICAgICAgIG5hbWU6ICdDb21tYW5kIFInLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0JhbGFuY2VkIG1vZGVsIGZvciBnZW5lcmFsIHVzZSBhbmQgUkFHJyxcbiAgICAgICAgY29udGV4dExlbmd0aDogMTI4MDAwLFxuICAgICAgICBwcmljaW5nOiAnJDAuNS8xTSBpbnB1dCwgJDEuNS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjUsXG4gICAgICAgIG91dHB1dFByaWNlOiAxLjVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnY29tbWFuZCcsXG4gICAgICAgIG5hbWU6ICdDb21tYW5kJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdWZXJzYXRpbGUgbW9kZWwgZm9yIHZhcmlvdXMgdGFza3MnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiA0MDk2LFxuICAgICAgICBwcmljaW5nOiAnJDEvMU0gaW5wdXQsICQyLzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDEsXG4gICAgICAgIG91dHB1dFByaWNlOiAyXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJ2NvbW1hbmQtbGlnaHQnLFxuICAgICAgICBuYW1lOiAnQ29tbWFuZCBMaWdodCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRmFzdCBhbmQgZWZmaWNpZW50IGZvciBzaW1wbGUgdGFza3MnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiA0MDk2LFxuICAgICAgICBwcmljaW5nOiAnJDAuMy8xTSBpbnB1dCwgJDAuNi8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjMsXG4gICAgICAgIG91dHB1dFByaWNlOiAwLjZcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnY29tbWFuZC1uaWdodGx5JyxcbiAgICAgICAgbmFtZTogJ0NvbW1hbmQgTmlnaHRseScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTGF0ZXN0IGV4cGVyaW1lbnRhbCBmZWF0dXJlcycsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDQwOTYsXG4gICAgICAgIHByaWNpbmc6ICckMS8xTSBpbnB1dCwgJDIvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMSxcbiAgICAgICAgb3V0cHV0UHJpY2U6IDJcbiAgICAgIH1cbiAgICBdXG4gIH0sXG4gIHtcbiAgICBpZDogJ2dyb3EnLFxuICAgIG5hbWU6ICdHcm9xJyxcbiAgICBpY29uOiAn4pqhJyxcbiAgICBkZXNjcmlwdGlvbjogJ0dyb3EgLSBVbHRyYS1mYXN0IGluZmVyZW5jZSB3aXRoIEdyb3FDaGlwIHRlY2hub2xvZ3knLFxuICAgIGJhc2VVcmw6ICdodHRwczovL2FwaS5ncm9xLmNvbS9vcGVuYWkvdjEnLFxuICAgIGFwaUtleVBsYWNlaG9sZGVyOiAnZ3NrXy4uLicsXG4gICAgaXNBY3RpdmU6IHRydWUsXG4gICAgc3VwcG9ydHNTdHJlYW1pbmc6IHRydWUsXG4gICAgbWF4VG9rZW5zOiAzMjc2OCxcbiAgICBtb2RlbHM6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdsbGFtYS0zLjEtNzBiLXZlcnNhdGlsZScsXG4gICAgICAgIG5hbWU6ICdMbGFtYSAzLjEgNzBCJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdNZXRhIExsYW1hIDMuMSA3MEIgb24gR3JvcScsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDEzMTA3MixcbiAgICAgICAgcHJpY2luZzogJyQwLjU5LzFNIGlucHV0LCAkMC43OS8xTSBvdXRwdXQnLFxuICAgICAgICBpbnB1dFByaWNlOiAwLjU5LFxuICAgICAgICBvdXRwdXRQcmljZTogMC43OVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICdsbGFtYS0zLjEtOGItaW5zdGFudCcsXG4gICAgICAgIG5hbWU6ICdMbGFtYSAzLjEgOEInLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ01ldGEgTGxhbWEgMy4xIDhCIG9uIEdyb3EnLFxuICAgICAgICBjb250ZXh0TGVuZ3RoOiAxMzEwNzIsXG4gICAgICAgIHByaWNpbmc6ICckMC4wNS8xTSBpbnB1dCwgJDAuMDgvMU0gb3V0cHV0JyxcbiAgICAgICAgaW5wdXRQcmljZTogMC4wNSxcbiAgICAgICAgb3V0cHV0UHJpY2U6IDAuMDhcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnbWl4dHJhbC04eDdiLTMyNzY4JyxcbiAgICAgICAgbmFtZTogJ01peHRyYWwgOHg3QicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTWlzdHJhbCBNaXh0cmFsIDh4N0Igb24gR3JvcScsXG4gICAgICAgIGNvbnRleHRMZW5ndGg6IDMyNzY4LFxuICAgICAgICBwcmljaW5nOiAnJDAuMjQvMU0gaW5wdXQsICQwLjI0LzFNIG91dHB1dCcsXG4gICAgICAgIGlucHV0UHJpY2U6IDAuMjQsXG4gICAgICAgIG91dHB1dFByaWNlOiAwLjI0XG4gICAgICB9XG4gICAgXVxuICB9XG5dO1xuXG4vKipcbiAqINin2YTYrdi12YjZhCDYudmE2Ykg2YXZgtiv2YUg2K7Yr9mF2Kkg2KjZiNin2LPYt9ipIElEXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRQcm92aWRlckJ5SWQoaWQ6IHN0cmluZyk6IExMTVByb3ZpZGVyIHwgdW5kZWZpbmVkIHtcbiAgcmV0dXJuIExMTV9QUk9WSURFUlNfREFUQUJBU0UuZmluZChwcm92aWRlciA9PiBwcm92aWRlci5pZCA9PT0gaWQpO1xufVxuXG4vKipcbiAqINin2YTYrdi12YjZhCDYudmE2Ykg2KzZhdmK2Lkg2YXZgtiv2YXZiiDYp9mE2K7Yr9mF2Kkg2KfZhNmG2LTYt9mK2YZcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEFjdGl2ZVByb3ZpZGVycygpOiBMTE1Qcm92aWRlcltdIHtcbiAgcmV0dXJuIExMTV9QUk9WSURFUlNfREFUQUJBU0UuZmlsdGVyKHByb3ZpZGVyID0+IHByb3ZpZGVyLmlzQWN0aXZlKTtcbn1cblxuLyoqXG4gKiDYp9mE2K3YtdmI2YQg2LnZhNmJINmG2YXZiNiw2Kwg2KjZiNin2LPYt9ipIHByb3ZpZGVyIElEINmIIG1vZGVsIElEXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRNb2RlbEJ5SWQocHJvdmlkZXJJZDogc3RyaW5nLCBtb2RlbElkOiBzdHJpbmcpIHtcbiAgY29uc3QgcHJvdmlkZXIgPSBnZXRQcm92aWRlckJ5SWQocHJvdmlkZXJJZCk7XG4gIHJldHVybiBwcm92aWRlcj8ubW9kZWxzLmZpbmQobW9kZWwgPT4gbW9kZWwuaWQgPT09IG1vZGVsSWQpO1xufVxuXG4vKipcbiAqINin2YTYqNit2Ksg2LnZhiDZhdmC2K/ZhdmKINin2YTYrtiv2YXYqVxuICovXG5leHBvcnQgZnVuY3Rpb24gc2VhcmNoUHJvdmlkZXJzKHF1ZXJ5OiBzdHJpbmcpOiBMTE1Qcm92aWRlcltdIHtcbiAgY29uc3QgbG93ZXJjYXNlUXVlcnkgPSBxdWVyeS50b0xvd2VyQ2FzZSgpO1xuICByZXR1cm4gTExNX1BST1ZJREVSU19EQVRBQkFTRS5maWx0ZXIocHJvdmlkZXIgPT5cbiAgICBwcm92aWRlci5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgcHJvdmlkZXIuZGVzY3JpcHRpb24udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhsb3dlcmNhc2VRdWVyeSkgfHxcbiAgICBwcm92aWRlci5tb2RlbHMuc29tZShtb2RlbCA9PiBcbiAgICAgIG1vZGVsLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhsb3dlcmNhc2VRdWVyeSkgfHxcbiAgICAgIG1vZGVsLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpXG4gICAgKVxuICApO1xufVxuXG4vKipcbiAqINiq2K3Yr9mK2K8gQmFzZSBVUkwg2KfZhNiq2YTZgtin2KbZiiDZhNmF2YLYr9mFINin2YTYrtiv2YXYqVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0UHJvdmlkZXJCYXNlVXJsKHByb3ZpZGVySWQ6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IHByb3ZpZGVyID0gZ2V0UHJvdmlkZXJCeUlkKHByb3ZpZGVySWQpO1xuICByZXR1cm4gcHJvdmlkZXI/LmJhc2VVcmwgfHwgJyc7XG59XG5cbi8qKlxuICog2KfZhNit2LXZiNmEINi52YTZiSBIZWFkZXJzINin2YTZhdi32YTZiNio2Kkg2YTZhdmC2K/ZhSDYp9mE2K7Yr9mF2KlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFByb3ZpZGVySGVhZGVycyhwcm92aWRlcklkOiBzdHJpbmcpOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+IHtcbiAgY29uc3QgcHJvdmlkZXIgPSBnZXRQcm92aWRlckJ5SWQocHJvdmlkZXJJZCk7XG4gIGNvbnN0IGJhc2VIZWFkZXJzID0gcHJvdmlkZXI/LmhlYWRlcnMgfHwge307XG5cbiAgLy8g2KXYttin2YHYqSBoZWFkZXJzINiu2KfYtdipINmE2YPZhCDZhdiy2YjYr1xuICBzd2l0Y2ggKHByb3ZpZGVySWQpIHtcbiAgICBjYXNlICdhbnRocm9waWMnOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uYmFzZUhlYWRlcnMsXG4gICAgICAgICdhbnRocm9waWMtdmVyc2lvbic6ICcyMDIzLTA2LTAxJ1xuICAgICAgfTtcbiAgICBjYXNlICdjb2hlcmUnOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uYmFzZUhlYWRlcnMsXG4gICAgICAgICdDb2hlcmUtVmVyc2lvbic6ICcyMDIyLTEyLTA2J1xuICAgICAgfTtcbiAgICBjYXNlICdtaXN0cmFsJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLmJhc2VIZWFkZXJzLFxuICAgICAgICAnVXNlci1BZ2VudCc6ICdDb250ZXh0S2l0LzEuMCdcbiAgICAgIH07XG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiBiYXNlSGVhZGVycztcbiAgfVxufVxuIl0sIm5hbWVzIjpbIkxMTV9QUk9WSURFUlNfREFUQUJBU0UiLCJpZCIsIm5hbWUiLCJpY29uIiwiZGVzY3JpcHRpb24iLCJiYXNlVXJsIiwiYXBpS2V5UGxhY2Vob2xkZXIiLCJpc0FjdGl2ZSIsInN1cHBvcnRzU3RyZWFtaW5nIiwibWF4VG9rZW5zIiwibW9kZWxzIiwiY29udGV4dExlbmd0aCIsInByaWNpbmciLCJpbnB1dFByaWNlIiwib3V0cHV0UHJpY2UiLCJoZWFkZXJzIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NJVEVfVVJMIiwiZ2V0UHJvdmlkZXJCeUlkIiwiZmluZCIsInByb3ZpZGVyIiwiZ2V0QWN0aXZlUHJvdmlkZXJzIiwiZmlsdGVyIiwiZ2V0TW9kZWxCeUlkIiwicHJvdmlkZXJJZCIsIm1vZGVsSWQiLCJtb2RlbCIsInNlYXJjaFByb3ZpZGVycyIsInF1ZXJ5IiwibG93ZXJjYXNlUXVlcnkiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwic29tZSIsImdldFByb3ZpZGVyQmFzZVVybCIsImdldFByb3ZpZGVySGVhZGVycyIsImJhc2VIZWFkZXJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/llmProviders.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();