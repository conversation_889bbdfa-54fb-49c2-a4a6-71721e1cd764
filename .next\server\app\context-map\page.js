/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/context-map/page";
exports.ids = ["app/context-map/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontext-map%2Fpage&page=%2Fcontext-map%2Fpage&appPaths=%2Fcontext-map%2Fpage&pagePath=private-next-app-dir%2Fcontext-map%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontext-map%2Fpage&page=%2Fcontext-map%2Fpage&appPaths=%2Fcontext-map%2Fpage&pagePath=private-next-app-dir%2Fcontext-map%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'context-map',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/context-map/page.tsx */ \"(rsc)/./src/app/context-map/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/context-map/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/context-map/page\",\n        pathname: \"/context-map\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontext-map%2Fpage&page=%2Fcontext-map%2Fpage&appPaths=%2Fcontext-map%2Fpage&pagePath=private-next-app-dir%2Fcontext-map%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Ccontext-map%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Ccontext-map%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/context-map/page.tsx */ \"(ssr)/./src/app/context-map/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjb250ZXh0LW1hcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Lz81Mjc3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxhcHBcXFxcY29udGV4dC1tYXBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Ccontext-map%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/StoreHydration.tsx */ \"(ssr)/./src/components/StoreHydration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1N0b3JlSHlkcmF0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUaGVtZVByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q3N0eWxlcyU1QyU1Q2dsYXNzLWVmZmVjdHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNzdHlsZXMlNUMlNUN0ZXh0LWltcHJvdmVtZW50cy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUE0STtBQUM1STtBQUNBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvPzJlZGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFN0b3JlSHlkcmF0aW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxUaGVtZVByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/context-map/page.tsx":
/*!**************************************!*\
  !*** ./src/app/context-map/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContextMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(ssr)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(ssr)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(ssr)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ContextMap() {\n    const { contextMap, updateContextMap } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const questions = [\n        {\n            id: \"timeContext\",\n            question: \"What is the temporal context of your project?\",\n            questionAr: \"ما هو السياق الزمني لمشروعك؟\",\n            placeholder: \"e.g., Global 24/7 support, Business hours EST, Real-time responses...\",\n            placeholderAr: \"مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...\",\n            aiSuggestion: \"Consider time zones, working hours, response time expectations, and any time-sensitive requirements.\",\n            aiSuggestionAr: \"فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.\",\n            promptTemplate: 'Help me optimize this temporal context for an AI project: \"{answer}\". Suggest improvements for better time management.'\n        },\n        {\n            id: \"language\",\n            question: \"What languages should your AI system support?\",\n            questionAr: \"ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟\",\n            placeholder: \"e.g., English primary, Arabic secondary, Multilingual support...\",\n            placeholderAr: \"مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...\",\n            type: \"text\",\n            aiSuggestion: \"Consider your target audience, regional requirements, and the complexity of multilingual support.\",\n            aiSuggestionAr: \"فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.\",\n            promptTemplate: 'Analyze this language requirement for an AI system: \"{answer}\". Suggest implementation strategies.'\n        },\n        {\n            id: \"location\",\n            question: \"What geographic regions or locations will this project serve?\",\n            questionAr: \"ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟\",\n            placeholder: \"e.g., Middle East, North America, Global, Specific cities...\",\n            placeholderAr: \"مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...\",\n            aiSuggestion: \"Think about regional regulations, cultural differences, and infrastructure requirements.\",\n            aiSuggestionAr: \"فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.\",\n            promptTemplate: 'Help me understand the geographic implications of this scope: \"{answer}\". What should I consider?'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateContextMap({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Context Map\",\n        titleAr: \"خريطة السياق\",\n        subtitle: \"Define the contextual framework for your AI project\",\n        subtitleAr: \"حدد الإطار السياقي لمشروع الذكاء الاصطناعي\",\n        emoji: \"\\uD83D\\uDDFA️\",\n        moduleKey: \"context-map\",\n        backLink: {\n            href: \"/project-definition\",\n            label: \"← Back to Project Definition\",\n            labelAr: \"← العودة لتعريف المشروع\"\n        },\n        nextLink: {\n            href: \"/emotional-tone\",\n            label: \"Next: Emotional Tone →\",\n            labelAr: \"التالي: النبرة العاطفية ←\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: contextMap,\n            moduleName: \"Context Map\",\n            moduleNameAr: \"خريطة السياق\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    id: question.id,\n                    question: question.question,\n                    questionAr: question.questionAr,\n                    placeholder: question.placeholder,\n                    placeholderAr: question.placeholderAr,\n                    value: contextMap[question.id] || \"\",\n                    onChange: (value)=>handleFieldChange(question.id, value),\n                    type: question.type,\n                    aiSuggestion: question.aiSuggestion,\n                    aiSuggestionAr: question.aiSuggestionAr,\n                    promptTemplate: question.promptTemplate\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/context-map/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AutoSaveIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/AutoSaveIndicator.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AutoSaveIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AutoSaveIndicator() {\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    // Simulate auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setSaving(true);\n            setTimeout(()=>{\n                setLastSaved(new Date());\n                setSaving(false);\n            }, 500);\n        }, 10000); // Auto-save every 10 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    if (!lastSaved && !saving) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg px-4 py-2 flex items-center space-x-2\",\n            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"جاري الحفظ...\" : \"Saving...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            isArabic ? \"تم الحفظ\" : \"Saved\",\n                            \" \",\n                            lastSaved?.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AutoSaveIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClearContentButton.tsx":
/*!***********************************************!*\
  !*** ./src/components/ClearContentButton.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClearContentButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ClearContentButton() {\n    const { currentLanguage, clearAllAnswers } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const translations = {\n        ar: {\n            clearAll: \"مسح جميع الإجابات\",\n            confirmMessage: \"هل أنت متأكد من مسح جميع الإجابات؟ لا يمكن التراجع عن هذا الإجراء.\",\n            success: \"تم مسح جميع الإجابات بنجاح\"\n        },\n        en: {\n            clearAll: \"Clear All Answers\",\n            confirmMessage: \"Are you sure you want to clear all answers? This action cannot be undone.\",\n            success: \"All answers cleared successfully\"\n        }\n    };\n    const t = translations[isArabic ? \"ar\" : \"en\"];\n    const handleClearAll = ()=>{\n        if (window.confirm(t.confirmMessage)) {\n            clearAllAnswers();\n            // Show success message\n            setTimeout(()=>{\n                alert(t.success);\n            }, 100);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClearAll,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-red-50 to-red-100 dark:from-gray-800 dark:to-gray-900 hover:from-red-100 hover:to-red-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        title: t.clearAll,\n        \"aria-label\": t.clearAll,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-6 h-6 text-red-500 dark:text-red-400 group-hover:text-red-600 dark:group-hover:text-red-300 transition-colors duration-300\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClearContentButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _LanguageToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _ClearContentButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClearContentButton */ \"(ssr)/./src/components/ClearContentButton.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Header({ title, subtitle, backLink, emoji }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_6__.useContextStore)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const isArabic = mounted ? currentLanguage === \"ar\" : false;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 flex gap-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"الصفحة الرئيسية\" : \"Home\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/settings\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"إعدادات API\" : \"API Settings\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClearContentButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageToggle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 pt-4\",\n                children: [\n                    backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: backLink.href,\n                        className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors\",\n                        children: backLink.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2 text-center\",\n                        children: [\n                            emoji && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 21\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto text-center\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModuleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressIndicator */ \"(ssr)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AutoSaveIndicator */ \"(ssr)/./src/components/AutoSaveIndicator.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ModuleLayout({ title, titleAr, subtitle, subtitleAr, emoji, moduleKey, backLink, nextLink, children, rightPanel }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    currentModule: moduleKey\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 lg:order-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-4\",\n                                        children: isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center ml-3\",\n                                                    children: \"الأسئلة الذكية\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"✍️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"✍️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center\",\n                                                    children: \"Smart Questions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-4\",\n                                        children: isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center ml-3\",\n                                                    children: \"المخرجات المجمّعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center\",\n                                                    children: \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex justify-between items-center mt-12 max-w-7xl mx-auto gap-6 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                    children: [\n                        backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: backLink.href,\n                            className: `flex items-center gap-3 px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                            children: [\n                                isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: isArabic ? backLink.labelAr : backLink.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 15\n                        }, this),\n                        nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: nextLink.href,\n                            className: `flex items-center gap-3 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: isArabic ? nextLink.labelAr : nextLink.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, this),\n                                isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModuleLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OutputPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/OutputPanel.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OutputPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction OutputPanel({ moduleData, moduleName, moduleNameAr }) {\n    const { currentLanguage, outputFormat, setOutputFormat } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تحويل البيانات إلى تنسيقات مختلفة\n    const generateMarkdown = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let markdown = `# ${title}\\n\\n`;\n        Object.entries(moduleData).forEach(([key, value])=>{\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                markdown += `## ${formattedKey}\\n${value}\\n\\n`;\n            }\n        });\n        return markdown;\n    };\n    const generateHTML = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let html = `<div class=\"module-output\">\\n  <h1>${title}</h1>\\n`;\n        Object.entries(moduleData).forEach(([key, value])=>{\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                html += `  <section>\\n    <h2>${formattedKey}</h2>\\n    <p>${value}</p>\\n  </section>\\n`;\n            }\n        });\n        html += \"</div>\";\n        return html;\n    };\n    const generateJSON = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter(([_, value])=>value && typeof value === \"string\" && value.trim()));\n        return JSON.stringify({\n            module: isArabic ? moduleNameAr : moduleName,\n            data: filteredData,\n            metadata: {\n                timestamp: new Date().toISOString(),\n                language: isArabic ? \"ar\" : \"en\",\n                version: \"1.0\"\n            }\n        }, null, 2);\n    };\n    const generateYAML = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter(([_, value])=>value && typeof value === \"string\" && value.trim()));\n        let yaml = `# ${isArabic ? moduleNameAr : moduleName}\\n`;\n        yaml += `# Generated: ${new Date().toISOString()}\\n\\n`;\n        Object.entries(filteredData).forEach(([key, value])=>{\n            const formattedKey = key.replace(/([A-Z])/g, \"_$1\").toLowerCase();\n            yaml += `${formattedKey}: |\\n`;\n            const lines = String(value || \"\").split(\"\\n\");\n            lines.forEach((line)=>{\n                yaml += `  ${line}\\n`;\n            });\n            yaml += \"\\n\";\n        });\n        return yaml;\n    };\n    const getCurrentOutput = ()=>{\n        switch(outputFormat){\n            case \"markdown\":\n                return generateMarkdown();\n            case \"html\":\n                return generateHTML();\n            case \"json\":\n                return generateJSON();\n            case \"yaml\":\n                return generateYAML();\n            default:\n                return generateMarkdown();\n        }\n    };\n    const handleCopyAll = async ()=>{\n        const output = getCurrentOutput();\n        await navigator.clipboard.writeText(output);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    const handleDownload = ()=>{\n        const output = getCurrentOutput();\n        const extensions = {\n            markdown: \"md\",\n            html: \"html\",\n            json: \"json\",\n            yaml: \"yml\"\n        };\n        const extension = extensions[outputFormat] || \"txt\";\n        const filename = `${moduleName.toLowerCase().replace(/\\s+/g, \"-\")}.${extension}`;\n        const mimeTypes = {\n            markdown: \"text/markdown\",\n            html: \"text/html\",\n            json: \"application/json\",\n            yaml: \"text/yaml\"\n        };\n        const mimeType = mimeTypes[outputFormat] || \"text/plain\";\n        const blob = new Blob([\n            output\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const hasData = Object.values(moduleData).some((value)=>value && typeof value === \"string\" && value.trim());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex items-center justify-between ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex flex-wrap gap-2 ${isArabic ? \"space-x-reverse space-x-2\" : \"space-x-2\"}`,\n                        children: [\n                            \"markdown\",\n                            \"html\",\n                            \"json\",\n                            \"yaml\"\n                        ].map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setOutputFormat(format),\n                                className: `px-3 py-1 text-sm rounded-lg transition-colors ${outputFormat === format ? \"bg-blue-600 text-white\" : \"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600\"}`,\n                                children: format.toUpperCase()\n                            }, format, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    hasData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex gap-2 ${isArabic ? \"space-x-reverse space-x-2\" : \"space-x-2\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyAll,\n                                className: \"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `relative flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: copied ? \"animate-bounce\" : \"group-hover:scale-110 transition-transform duration-300\",\n                                                children: \"\\uD83D\\uDCCB\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ الكل\" : \"Copy All\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white shadow-md hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105 active:scale-95\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `relative flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"group-hover:scale-110 transition-transform duration-300\",\n                                                children: \"\\uD83D\\uDCBE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            isArabic ? \"تحميل\" : \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]\",\n                children: hasData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: `text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto ${isArabic ? \"text-right\" : \"text-left\"}`,\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    children: getCurrentOutput()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        dir: isArabic ? \"rtl\" : \"ltr\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-4xl mb-2 block\",\n                                children: \"\\uD83D\\uDCDD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: isArabic ? \"ابدأ بالإجابة على الأسئلة لرؤية المخرجات\" : \"Start answering questions to see outputs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-xs text-gray-500 dark:text-gray-400 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                children: [\n                    outputFormat === \"markdown\" && (isArabic ? \"تنسيق Markdown - جاهز للاستخدام في المستندات\" : \"Markdown format - Ready for documentation\"),\n                    outputFormat === \"html\" && (isArabic ? \"تنسيق HTML - جاهز للمواقع الإلكترونية\" : \"HTML format - Ready for websites\"),\n                    outputFormat === \"json\" && (isArabic ? \"تنسيق JSON - جاهز للبرمجة والـ APIs\" : \"JSON format - Ready for programming and APIs\"),\n                    outputFormat === \"yaml\" && (isArabic ? \"تنسيق YAML - جاهز للتكوين والنشر\" : \"YAML format - Ready for configuration and deployment\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OutputPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProgressIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProgressIndicator.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProgressIndicator({ currentModule }) {\n    const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const modules = [\n        {\n            key: \"project-definition\",\n            name: \"Project Definition\",\n            nameAr: \"تعريف المشروع\",\n            emoji: \"\\uD83C\\uDFAF\",\n            data: projectDefinition,\n            href: \"/project-definition\"\n        },\n        {\n            key: \"context-map\",\n            name: \"Context Map\",\n            nameAr: \"خريطة السياق\",\n            emoji: \"\\uD83D\\uDDFA️\",\n            data: contextMap,\n            href: \"/context-map\"\n        },\n        {\n            key: \"emotional-tone\",\n            name: \"Emotional Tone\",\n            nameAr: \"النبرة العاطفية\",\n            emoji: \"✨\",\n            data: emotionalTone,\n            href: \"/emotional-tone\"\n        },\n        {\n            key: \"technical-layer\",\n            name: \"Technical Layer\",\n            nameAr: \"الطبقة التقنية\",\n            emoji: \"⚙️\",\n            data: technicalLayer,\n            href: \"/technical-layer\"\n        },\n        {\n            key: \"legal-risk\",\n            name: \"Legal & Privacy\",\n            nameAr: \"القانونية والخصوصية\",\n            emoji: \"\\uD83D\\uDD12\",\n            data: legalRisk,\n            href: \"/legal-risk\"\n        }\n    ];\n    const getModuleProgress = (moduleData)=>{\n        const totalFields = Object.keys(moduleData).length;\n        const filledFields = Object.values(moduleData).filter((value)=>value && typeof value === \"string\" && value.trim()).length;\n        return totalFields > 0 ? filledFields / totalFields * 100 : 0;\n    };\n    const overallProgress = modules.reduce((total, module)=>{\n        return total + getModuleProgress(module.data);\n    }, 0) / modules.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex items-center justify-between mb-4 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                        children: isArabic ? \"تقدم المشروع\" : \"Project Progress\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            Math.round(overallProgress),\n                            \"% \",\n                            isArabic ? \"مكتمل\" : \"Complete\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `bg-blue-600 h-2 rounded-full transition-all duration-300 ${isArabic ? \"ml-auto\" : \"\"}`,\n                    style: {\n                        width: `${overallProgress}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                children: modules.map((module)=>{\n                    const progress = getModuleProgress(module.data);\n                    const isCurrent = currentModule === module.key;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: module.href,\n                        className: `p-3 rounded-lg border-2 transition-all hover:shadow-md ${isCurrent ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl mb-2\",\n                                    children: module.emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: isArabic ? module.nameAr : module.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `h-1 rounded-full transition-all duration-300 ${progress === 100 ? \"bg-green-500\" : \"bg-blue-500\"}`,\n                                        style: {\n                                            width: `${progress}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        Math.round(progress),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    }, module.key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-6 flex justify-center gap-4 ${isArabic ? \"space-x-reverse space-x-4\" : \"space-x-4\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/final-preview\",\n                        className: `flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: isArabic ? \"ml-1\" : \"mr-1\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    overallProgress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            if (confirm(isArabic ? \"هل أنت متأكد من إعادة تعيين جميع البيانات؟\" : \"Are you sure you want to reset all data?\")) {\n                                // Reset functionality would go here\n                                window.location.reload();\n                            }\n                        },\n                        className: `flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: isArabic ? \"ml-1\" : \"mr-1\",\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            isArabic ? \"إعادة تعيين\" : \"Reset\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProgressIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartFieldAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SmartFieldAssistant({ fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" }) {\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider?.name, \"with model:\", provider?.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            // إضافة timeout للطلب من جانب العميل\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                controller.abort();\n            }, 35000); // 35 ثانية timeout\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(`API Error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            let errorMessage = translations.error;\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    errorMessage = translations.timeout;\n                } else if (error.message.includes(\"timeout\")) {\n                    errorMessage = translations.timeout;\n                } else {\n                    errorMessage = `${translations.error}: ${error.message}`;\n                }\n            }\n            onValueChange(errorMessage);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        // تحسين الـ prompts لتكون أكثر تفصيلاً وذكاءً\n        const fieldPrompts = {\n            // Project Definition Module\n            name: {\n                ar: `بناءً على السياق المتاح، اقترح 3 أسماء إبداعية ومناسبة للمشروع`,\n                en: `Based on the available context, suggest 3 creative and suitable project names`\n            },\n            purpose: {\n                ar: `اكتب 3 أوصاف مختلفة ومفصلة لغرض المشروع، مع مراعاة الاسم والسياق`,\n                en: `Write 3 different and detailed project purpose descriptions, considering the name and context`\n            },\n            targetUsers: {\n                ar: `حدد 3 مجموعات مختلفة من المستخدمين المستهدفين بناءً على غرض المشروع`,\n                en: `Define 3 different target user groups based on the project purpose`\n            },\n            goals: {\n                ar: `اقترح 3 أهداف محددة وقابلة للقياس للمشروع`,\n                en: `Suggest 3 specific and measurable project goals`\n            },\n            scope: {\n                ar: `حدد 3 نطاقات مختلفة للمشروع (صغير، متوسط، كبير)`,\n                en: `Define 3 different project scopes (small, medium, large)`\n            },\n            timeline: {\n                ar: `اقترح 3 جداول زمنية مختلفة للمشروع`,\n                en: `Suggest 3 different project timelines`\n            },\n            // Context Map Module\n            timeContext: {\n                ar: `حدد 3 سياقات زمنية مختلفة مناسبة للمشروع`,\n                en: `Define 3 different time contexts suitable for the project`\n            },\n            language: {\n                ar: `اقترح 3 استراتيجيات لغوية للمشروع`,\n                en: `Suggest 3 language strategies for the project`\n            },\n            location: {\n                ar: `حدد 3 مواقع جغرافية مستهدفة للمشروع`,\n                en: `Define 3 target geographical locations for the project`\n            },\n            culturalContext: {\n                ar: `اقترح 3 اعتبارات ثقافية مهمة للمشروع`,\n                en: `Suggest 3 important cultural considerations for the project`\n            },\n            // Emotional Tone Module\n            personality: {\n                ar: `اقترح 3 شخصيات مختلفة للمشروع تناسب المستخدمين المستهدفين`,\n                en: `Suggest 3 different project personalities that suit the target users`\n            },\n            communicationStyle: {\n                ar: `حدد 3 أساليب تواصل مختلفة مناسبة للمشروع`,\n                en: `Define 3 different communication styles suitable for the project`\n            },\n            // Technical Layer Module\n            programmingLanguages: {\n                ar: `اقترح 3 لغات برمجة مناسبة للمشروع مع التبرير`,\n                en: `Suggest 3 suitable programming languages for the project with justification`\n            },\n            frameworks: {\n                ar: `حدد 3 إطارات عمل تقنية مناسبة للمشروع`,\n                en: `Define 3 technical frameworks suitable for the project`\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? `اقترح محتوى ذكي ومناسب لـ ${fieldName}` : `Suggest smart and suitable content for ${fieldName}`;\n        // بناء سياق أكثر ذكاءً\n        const contextInfo = buildIntelligentContext(context, fieldName, isArabic);\n        const instructions = isArabic ? `قدم 3 اقتراحات مرقمة ومفصلة، كل اقتراح في سطر منفصل. اجعل كل اقتراح متماسكاً مع السياق العام للمشروع.` : `Provide 3 numbered and detailed suggestions, each on a separate line. Make each suggestion coherent with the overall project context.`;\n        return `${contextInfo}\\n${basePrompt}\\n${instructions}`;\n    };\n    // وظيفة لبناء سياق ذكي\n    const buildIntelligentContext = (context, fieldName, isArabic)=>{\n        const contextParts = [];\n        // معلومات المشروع الأساسية\n        if (context?.projectDefinition?.name) {\n            contextParts.push(isArabic ? `اسم المشروع: ${context.projectDefinition.name}` : `Project Name: ${context.projectDefinition.name}`);\n        }\n        if (context?.projectDefinition?.purpose) {\n            contextParts.push(isArabic ? `الغرض: ${context.projectDefinition.purpose.substring(0, 100)}...` : `Purpose: ${context.projectDefinition.purpose.substring(0, 100)}...`);\n        }\n        if (context?.projectDefinition?.targetUsers) {\n            contextParts.push(isArabic ? `المستخدمون المستهدفون: ${context.projectDefinition.targetUsers.substring(0, 80)}...` : `Target Users: ${context.projectDefinition.targetUsers.substring(0, 80)}...`);\n        }\n        // سياق إضافي حسب المجال\n        if (fieldName.includes(\"technical\") || fieldName.includes(\"programming\") || fieldName.includes(\"frameworks\")) {\n            if (context?.technicalLayer?.programmingLanguages) {\n                contextParts.push(isArabic ? `التقنيات المستخدمة: ${context.technicalLayer.programmingLanguages.substring(0, 60)}...` : `Technologies: ${context.technicalLayer.programmingLanguages.substring(0, 60)}...`);\n            }\n        }\n        if (fieldName.includes(\"emotional\") || fieldName.includes(\"personality\") || fieldName.includes(\"communication\")) {\n            if (context?.emotionalTone?.personality) {\n                contextParts.push(isArabic ? `الشخصية المطلوبة: ${context.emotionalTone.personality.substring(0, 60)}...` : `Required Personality: ${context.emotionalTone.personality.substring(0, 60)}...`);\n            }\n        }\n        return contextParts.length > 0 ? (isArabic ? \"السياق الحالي:\\n\" : \"Current Context:\\n\") + contextParts.join(\"\\n\") : isArabic ? \"مشروع جديد\" : \"New Project\";\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center gap-2 ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83D\\uDCC4 \",\n                            isArabic ? \"توليد بالذكاء الاصطناعي\" : \"Generate with AI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 352,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 351,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center gap-2 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: `relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group ${hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\"} ${isGenerating ? \"animate-pulse scale-105\" : \"\"} ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                title: hasValidProvider ? isArabic ? \"توليد سريع محسّن - أقل من 5 ثواني\" : \"Fast optimized generation - under 5 seconds\" : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `relative flex items-center gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.fastGeneration\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: `relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `relative flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmartFieldAssistant.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmartQuestion.tsx":
/*!******************************************!*\
  !*** ./src/components/SmartQuestion.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartQuestion)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SmartFieldAssistant */ \"(ssr)/./src/components/SmartFieldAssistant.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SmartQuestion({ id, question, questionAr, placeholder, placeholderAr, value, onChange, type = \"textarea\", aiSuggestion, aiSuggestionAr, promptTemplate }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const handleCopy = async ()=>{\n        if (value.trim()) {\n            await navigator.clipboard.writeText(value);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handlePromptCopy = async ()=>{\n        if (promptTemplate && value.trim()) {\n            const prompt = promptTemplate.replace(\"{answer}\", value);\n            await navigator.clipboard.writeText(prompt);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handleSuggestionApply = ()=>{\n        if (aiSuggestion || aiSuggestionAr) {\n            const suggestion = isArabic ? aiSuggestionAr : aiSuggestion;\n            if (suggestion && !value.trim()) {\n                onChange(suggestion);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg`,\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: `text-lg font-medium text-gray-900 dark:text-white mb-2 w-full ${isArabic ? \"text-right\" : \"text-left\"}`,\n                            style: isArabic ? {\n                                textAlign: \"right\",\n                                direction: \"rtl\"\n                            } : {\n                                textAlign: \"left\",\n                                direction: \"ltr\"\n                            },\n                            children: isArabic ? questionAr : question\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-full ${isArabic ? \"text-right\" : \"text-left\"}`,\n                            style: isArabic ? {\n                                textAlign: \"right\",\n                                direction: \"rtl\"\n                            } : {\n                                textAlign: \"left\",\n                                direction: \"ltr\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSuggestion(!showSuggestion),\n                                className: `text-sm text-blue-600 dark:text-blue-400 hover:underline inline-flex items-center ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                style: isArabic ? {\n                                    textAlign: \"right\",\n                                    direction: \"rtl\"\n                                } : {\n                                    textAlign: \"left\",\n                                    direction: \"ltr\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: isArabic ? \"ml-1\" : \"mr-1\",\n                                        children: \"\\uD83E\\uDDE0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    isArabic ? \"عرض الاقتراح الذكي\" : \"Show AI Suggestion\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            showSuggestion && (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\",\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex items-start w-full ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `text-blue-500 ${isArabic ? \"ml-2\" : \"mr-2\"}`,\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: `text-sm text-blue-800 dark:text-blue-200 w-full ${isArabic ? \"text-right\" : \"text-left\"}`,\n                            style: isArabic ? {\n                                textAlign: \"right\",\n                                direction: \"rtl\"\n                            } : {\n                                textAlign: \"left\",\n                                direction: \"ltr\"\n                            },\n                            children: isArabic ? aiSuggestionAr : aiSuggestion\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this),\n            type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: `w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-black dark:text-white resize-none font-arabic ${isArabic ? \"text-right\" : \"text-left\"}`,\n                rows: 4,\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                style: {\n                    fontFamily: isArabic ? \"'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif\" : \"inherit\",\n                    lineHeight: isArabic ? \"1.8\" : \"1.5\",\n                    letterSpacing: isArabic ? \"0.02em\" : \"normal\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: `w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-black dark:text-white font-arabic ${isArabic ? \"text-right\" : \"text-left\"}`,\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                style: {\n                    fontFamily: isArabic ? \"'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif\" : \"inherit\",\n                    lineHeight: isArabic ? \"1.8\" : \"1.5\",\n                    letterSpacing: isArabic ? \"0.02em\" : \"normal\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex items-center gap-2 pt-2 w-full ${isArabic ? \"flex-row-reverse justify-end\" : \"justify-start\"}`,\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                style: isArabic ? {\n                    direction: \"rtl\"\n                } : {\n                    direction: \"ltr\"\n                },\n                children: [\n                    promptTemplate && value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handlePromptCopy,\n                        className: `relative flex items-center px-3 py-2.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95 flex-shrink-0 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"group-hover:scale-110 transition-transform duration-300\",\n                                        children: \"\\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    isArabic ? \"نسخ كـ Prompt\" : \"Copy as Prompt\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"relative flex items-center px-3 py-2.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: copied ? \"animate-bounce\" : \"group-hover:scale-110 transition-transform duration-300\",\n                                        children: \"\\uD83D\\uDCCE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ\" : \"Copy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        fieldName: id,\n                        fieldValue: value,\n                        onValueChange: onChange,\n                        placeholder: isArabic ? placeholderAr : placeholder,\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmartQuestion.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoreHydration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction StoreHydration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Manually hydrate the store to prevent hydration mismatches\n        const store = _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore.getState();\n        if (false) {}\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs2REFFa0M7QUFDcUI7QUFFeEMsU0FBU0U7SUFDdEJGLGdEQUFTQSxDQUFDO1FBQ1IsNkRBQTZEO1FBQzdELE1BQU1HLFFBQVFGLGdFQUFlQSxDQUFDRyxRQUFRO1FBQ3RDLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUNILEdBQUcsRUFBRTtJQUVMLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3g/YTI5NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUNvbnRleHRTdG9yZSB9IGZyb20gJ0Avc3RvcmUvY29udGV4dFN0b3JlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3RvcmVIeWRyYXRpb24oKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gTWFudWFsbHkgaHlkcmF0ZSB0aGUgc3RvcmUgdG8gcHJldmVudCBoeWRyYXRpb24gbWlzbWF0Y2hlc1xuICAgIGNvbnN0IHN0b3JlID0gdXNlQ29udGV4dFN0b3JlLmdldFN0YXRlKCk7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB1c2VDb250ZXh0U3RvcmUucGVyc2lzdC5yZWh5ZHJhdGUoKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICByZXR1cm4gbnVsbDtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VDb250ZXh0U3RvcmUiLCJTdG9yZUh5ZHJhdGlvbiIsInN0b3JlIiwiZ2V0U3RhdGUiLCJwZXJzaXN0IiwicmVoeWRyYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StoreHydration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage only, ignore system preference after first load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"contextkit-theme\");\n        if (savedTheme && (savedTheme === \"light\" || savedTheme === \"dark\")) {\n            // إذا كان هناك theme محفوظ، استخدمه\n            setTheme(savedTheme);\n        } else {\n            // فقط في المرة الأولى، استخدم system preference\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            setTheme(systemTheme);\n            localStorage.setItem(\"contextkit-theme\", systemTheme);\n        }\n        setMounted(true);\n    }, []);\n    // Apply theme to document immediately\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            // إزالة جميع classes المتعلقة بالـ theme أولاً\n            root.classList.remove(\"light\", \"dark\");\n            // إضافة الـ class الصحيح\n            root.classList.add(theme);\n            // حفظ في localStorage مع مفتاح مخصص\n            localStorage.setItem(\"contextkit-theme\", theme);\n            // تطبيق فوري على body أيضاً\n            document.body.setAttribute(\"data-theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        // حفظ فوري في localStorage\n        localStorage.setItem(\"contextkit-theme\", newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVRvZ2dsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMkM7QUFFNUIsU0FBU0M7SUFDdEIsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLFdBQVcsRUFBRSxHQUFHSCx3REFBUUE7SUFFdkMscUJBQ0UsOERBQUNJO1FBQ0NDLFNBQVNGO1FBQ1RHLFdBQVU7UUFDVkMsY0FBWSxDQUFDLFVBQVUsRUFBRUwsVUFBVSxVQUFVLFNBQVMsUUFBUSxLQUFLLENBQUM7UUFDcEVNLE9BQU8sQ0FBQyxVQUFVLEVBQUVOLFVBQVUsVUFBVSxTQUFTLFFBQVEsS0FBSyxDQUFDOzswQkFFL0QsOERBQUNPO2dCQUFJSCxXQUFVOztrQ0FFYiw4REFBQ0k7d0JBQ0NKLFdBQVcsQ0FBQywrREFBK0QsRUFDekVKLFVBQVUsVUFDTixtQ0FDQSwrQkFDTCxDQUFDO3dCQUNGUyxNQUFLO3dCQUNMQyxRQUFPO3dCQUNQQyxTQUFRO3dCQUNSQyxPQUFNO2tDQUVOLDRFQUFDQzs0QkFDQ0MsZUFBYzs0QkFDZEMsZ0JBQWU7NEJBQ2ZDLGFBQWE7NEJBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O2tDQUtOLDhEQUFDVDt3QkFDQ0osV0FBVyxDQUFDLHVFQUF1RSxFQUNqRkosVUFBVSxTQUNOLG1DQUNBLGdDQUNMLENBQUM7d0JBQ0ZTLE1BQUs7d0JBQ0xDLFFBQU87d0JBQ1BDLFNBQVE7d0JBQ1JDLE9BQU07a0NBRU4sNEVBQUNDOzRCQUNDQyxlQUFjOzRCQUNkQyxnQkFBZTs0QkFDZkMsYUFBYTs0QkFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVIsOERBQUNWO2dCQUFJSCxXQUFVOzs7Ozs7Ozs7Ozs7QUFHckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Ly4vc3JjL2NvbXBvbmVudHMvVGhlbWVUb2dnbGUudHN4PzU0MGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4vVGhlbWVQcm92aWRlcic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lVG9nZ2xlKCkge1xuICBjb25zdCB7IHRoZW1lLCB0b2dnbGVUaGVtZSB9ID0gdXNlVGhlbWUoKTtcblxuICByZXR1cm4gKFxuICAgIDxidXR0b25cbiAgICAgIG9uQ2xpY2s9e3RvZ2dsZVRoZW1lfVxuICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy0xMiBoLTEyIHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby0xMDAgZGFyazpmcm9tLWdyYXktODAwIGRhcms6dG8tZ3JheS05MDAgaG92ZXI6ZnJvbS1ibHVlLTEwMCBob3Zlcjp0by1pbmRpZ28tMjAwIGRhcms6aG92ZXI6ZnJvbS1ncmF5LTcwMCBkYXJrOmhvdmVyOnRvLWdyYXktODAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDAgZ3JvdXAgb3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgIGFyaWEtbGFiZWw9e2BTd2l0Y2ggdG8gJHt0aGVtZSA9PT0gJ2xpZ2h0JyA/ICdkYXJrJyA6ICdsaWdodCd9IG1vZGVgfVxuICAgICAgdGl0bGU9e2BTd2l0Y2ggdG8gJHt0aGVtZSA9PT0gJ2xpZ2h0JyA/ICdkYXJrJyA6ICdsaWdodCd9IG1vZGVgfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICB7LyogU3VuIGljb24gKi99XG4gICAgICAgIDxzdmdcbiAgICAgICAgICBjbGFzc05hbWU9e2B3LTYgaC02IHRleHQtYW1iZXItNTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgdGhlbWUgPT09ICdsaWdodCdcbiAgICAgICAgICAgICAgPyAnb3BhY2l0eS0xMDAgcm90YXRlLTAgc2NhbGUtMTAwJ1xuICAgICAgICAgICAgICA6ICdvcGFjaXR5LTAgcm90YXRlLTE4MCBzY2FsZS0wJ1xuICAgICAgICAgIH1gfVxuICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICA+XG4gICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgZD1cIk0xMiAzdjFtMCAxNnYxbTktOWgtMU00IDEySDNtMTUuMzY0IDYuMzY0bC0uNzA3LS43MDdNNi4zNDMgNi4zNDNsLS43MDctLjcwN20xMi43MjggMGwtLjcwNy43MDdNNi4zNDMgMTcuNjU3bC0uNzA3LjcwN00xNiAxMmE0IDQgMCAxMS04IDAgNCA0IDAgMDE4IDB6XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L3N2Zz5cblxuICAgICAgICB7LyogTW9vbiBpY29uICovfVxuICAgICAgICA8c3ZnXG4gICAgICAgICAgY2xhc3NOYW1lPXtgdy02IGgtNiB0ZXh0LWJsdWUtNDAwIGFic29sdXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgdGhlbWUgPT09ICdkYXJrJ1xuICAgICAgICAgICAgICA/ICdvcGFjaXR5LTEwMCByb3RhdGUtMCBzY2FsZS0xMDAnXG4gICAgICAgICAgICAgIDogJ29wYWNpdHktMCAtcm90YXRlLTE4MCBzY2FsZS0wJ1xuICAgICAgICAgIH1gfVxuICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICA+XG4gICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgZD1cIk0yMC4zNTQgMTUuMzU0QTkgOSAwIDAxOC42NDYgMy42NDYgOS4wMDMgOS4wMDMgMCAwMDEyIDIxYTkuMDAzIDkuMDAzIDAgMDA4LjM1NC01LjY0NnpcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTdWJ0bGUgZ2xvdyBlZmZlY3QgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS13aGl0ZS8xMCB0by10cmFuc3BhcmVudCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMFwiIC8+XG4gICAgPC9idXR0b24+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlVGhlbWUiLCJUaGVtZVRvZ2dsZSIsInRoZW1lIiwidG9nZ2xlVGhlbWUiLCJidXR0b24iLCJvbkNsaWNrIiwiY2xhc3NOYW1lIiwiYXJpYS1sYWJlbCIsInRpdGxlIiwiZGl2Iiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJ4bWxucyIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(`Provider with id ${provider.id} already exists`);\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                // إضافة القيم الافتراضية للميزات المتقدمة\n                const enhancedProvider = {\n                    ...provider,\n                    priority: provider.priority || 5,\n                    isBackup: provider.isBackup || false,\n                    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,\n                    timeout: provider.timeout || 30,\n                    retryAttempts: provider.retryAttempts || 3,\n                    stats: provider.stats || {\n                        totalRequests: 0,\n                        successfulRequests: 0,\n                        failedRequests: 0,\n                        averageResponseTime: 0,\n                        totalTokensUsed: 0,\n                        totalCost: 0\n                    }\n                };\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            enhancedProvider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        // ميزات متقدمة للمزودين\n        getProvidersByPriority: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled).sort((a, b)=>(b.priority || 5) - (a.priority || 5));\n        },\n        getBackupProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled && p.isBackup);\n        },\n        updateProviderStats: (id, stats)=>set((state)=>{\n                const providers = state.apiSettings.providers || [];\n                const providerIndex = providers.findIndex((p)=>p.id === id);\n                if (providerIndex !== -1) {\n                    const updatedProviders = [\n                        ...providers\n                    ];\n                    updatedProviders[providerIndex] = {\n                        ...updatedProviders[providerIndex],\n                        stats: {\n                            ...updatedProviders[providerIndex].stats,\n                            ...stats,\n                            lastUsed: new Date()\n                        }\n                    };\n                    return {\n                        apiSettings: {\n                            ...state.apiSettings,\n                            providers: updatedProviders\n                        }\n                    };\n                }\n                return state;\n            }),\n        getBestProvider: (criteria = \"reliability\")=>{\n            const state = get();\n            const activeProviders = (state.apiSettings.providers || []).filter((p)=>p.isEnabled && !p.isBackup);\n            if (activeProviders.length === 0) return undefined;\n            switch(criteria){\n                case \"speed\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestSpeed = best.stats?.averageResponseTime || Infinity;\n                        const currentSpeed = current.stats?.averageResponseTime || Infinity;\n                        return currentSpeed < bestSpeed ? current : best;\n                    });\n                case \"cost\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestCost = best.costPerToken || Infinity;\n                        const currentCost = current.costPerToken || Infinity;\n                        return currentCost < bestCost ? current : best;\n                    });\n                case \"reliability\":\n                default:\n                    return activeProviders.reduce((best, current)=>{\n                        const bestReliability = best.stats ? best.stats.successfulRequests / (best.stats.totalRequests || 1) : 0;\n                        const currentReliability = current.stats ? current.stats.successfulRequests / (current.stats.totalRequests || 1) : 0;\n                        return currentReliability > bestReliability ? current : best;\n                    });\n            }\n        },\n        resetAll: ()=>set(initialState),\n        // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)\n        clearAllAnswers: ()=>set((state)=>({\n                    ...state,\n                    projectDefinition: {\n                        name: \"\",\n                        purpose: \"\",\n                        targetUsers: \"\",\n                        goals: \"\",\n                        scope: \"\",\n                        timeline: \"\"\n                    },\n                    contextMap: {\n                        timeContext: \"\",\n                        language: \"\",\n                        location: \"\",\n                        culturalContext: \"\",\n                        behavioralAspects: \"\",\n                        environmentalFactors: \"\"\n                    },\n                    emotionalTone: {\n                        personality: \"\",\n                        communicationStyle: \"\",\n                        userExperience: \"\",\n                        brandVoice: \"\",\n                        emotionalIntelligence: \"\",\n                        interactionFlow: \"\"\n                    },\n                    technicalLayer: {\n                        programmingLanguages: \"\",\n                        frameworks: \"\",\n                        llmModels: \"\",\n                        databases: \"\",\n                        apis: \"\",\n                        infrastructure: \"\"\n                    },\n                    legalRisk: {\n                        privacyConcerns: \"\",\n                        dataProtection: \"\",\n                        compliance: \"\",\n                        risks: \"\",\n                        mitigation: \"\",\n                        ethicalConsiderations: \"\"\n                    }\n                })),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1,\n    skipHydration: true\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"22d6d015889c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2UzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyZDZkMDE1ODg5Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/glass-effects.css":
/*!**************************************!*\
  !*** ./src/styles/glass-effects.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e0b1f04c4ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9zdHlsZXMvZ2xhc3MtZWZmZWN0cy5jc3M/NWRiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVlMGIxZjA0YzRlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/glass-effects.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/text-improvements.css":
/*!******************************************!*\
  !*** ./src/styles/text-improvements.css ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"757aecea32c0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcz8wYmQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzU3YWVjZWEzMmMwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/text-improvements.css\n");

/***/ }),

/***/ "(rsc)/./src/app/context-map/page.tsx":
/*!**************************************!*\
  !*** ./src/app/context-map/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\context-map\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\context-map\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_glass_effects_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/glass-effects.css */ \"(rsc)/./src/styles/glass-effects.css\");\n/* harmony import */ var _styles_text_improvements_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/text-improvements.css */ \"(rsc)/./src/styles/text-improvements.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StoreHydration */ \"(rsc)/./src/components/StoreHydration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"ContextKit - AI Context Builder\",\n    description: \"Create organized, actionable context for AI-driven projects\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontext-map%2Fpage&page=%2Fcontext-map%2Fpage&appPaths=%2Fcontext-map%2Fpage&pagePath=private-next-app-dir%2Fcontext-map%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();