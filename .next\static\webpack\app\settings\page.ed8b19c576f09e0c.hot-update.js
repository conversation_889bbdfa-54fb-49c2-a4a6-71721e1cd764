"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/llmProviders */ \"(app-pages-browser)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(app-pages-browser)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { currentLanguage, apiSettings, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو الخدمة\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح API\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق...\" : \"Validating...\",\n        valid: isArabic ? \"صالح\" : \"Valid\",\n        invalid: isArabic ? \"غير صالح\" : \"Invalid\",\n        error: isArabic ? \"خطأ\" : \"Error\",\n        models: isArabic ? \"النماذج المتاحة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المحددة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تعديل النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ النماذج\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم تحديد أي نماذج\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة للرئيسية\" : \"Back to Home\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(isArabic ? \"يرجى اختيار مقدم خدمة\" : \"Please select a provider\");\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة غير موجود\" : \"Provider not found\");\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة موجود بالفعل\" : \"Provider already exists\");\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\");\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 \".concat(isArabic ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-arabic\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 font-arabic\",\n                                                children: isArabic ? \"لم يتم إعداد أي مقدم خدمة بعد\" : \"No providers configured yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"provider-header flex items-start justify-between mb-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"provider-info flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"provider-controls flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                getStatusIcon((validationState === null || validationState === void 0 ? void 0 : validationState.status) || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap\",\n                                                                            children: isArabic ? \"نشط\" : \"Active\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    className: \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-grid grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    className: \"w-full px-3 py-2 \".concat(isArabic ? \"pr-3 pl-10\" : \"pr-10 pl-3\", \" border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"),\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 348,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    className: \"absolute \".concat(isArabic ? \"left-3\" : \"right-3\", \" top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"),\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 360,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 360,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 356,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full mt-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                    children: [\n                                                                        translations.models,\n                                                                        \": \",\n                                                                        providerInfo.models.length\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleValidateProvider(provider.id),\n                                                                    disabled: !provider.apiKey || (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\",\n                                                                    className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\" ? translations.validating : translations.testConnection\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (validationState === null || validationState === void 0 ? void 0 : validationState.message) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-2 rounded text-sm \".concat(validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"),\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                                            children: translations.selectedModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setExpandedProviders((prev)=>({\n                                                                                        ...prev,\n                                                                                        [provider.id]: !prev[provider.id]\n                                                                                    }));\n                                                                                if (!selectedModels[provider.id]) {\n                                                                                    setSelectedModels((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: provider.selectedModels || []\n                                                                                        }));\n                                                                                }\n                                                                            },\n                                                                            className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic\",\n                                                                            children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500 dark:text-gray-400 font-arabic\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-2\",\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"model-checkbox flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 460,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"model-info flex-1 min-w-0\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 467,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 470,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 466,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 456,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 454,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 481,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\",\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 485,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            className: \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 498,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 493,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 484,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-2\",\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-arabic\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 515,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    className: \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 520,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 516,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 511,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic\",\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error-message flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-700 dark:text-red-300 font-arabic\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-buttons flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic\",\n                                    children: isArabic ? \"إلغاء\" : \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic\",\n                                    children: isArabic ? \"إضافة\" : \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 556,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 555,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"E2yalVrLtfODjnaMswjgodk23ig=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});