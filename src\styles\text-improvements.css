/* ===== ContextKit - تحسينات RTL والخطوط العربية ===== */

/* ===== الخطوط العربية ===== */
.font-arabic {
  font-family: '<PERSON><PERSON><PERSON>', 'Cairo', '<PERSON><PERSON>', 'Noto Sans Arabic', sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* ===== تحسينات الألوان ===== */

/* Light Mode */
.light-text-black {
  color: #000000 !important;
}

textarea:not(.dark textarea),
input[type="text"]:not(.dark input[type="text"]) {
  color: #000000 !important;
  background-color: #ffffff !important;
}

textarea::placeholder:not(.dark textarea::placeholder),
input[type="text"]::placeholder:not(.dark input[type="text"]::placeholder) {
  color: #6b7280 !important;
}

/* Dark Mode */
.dark textarea,
.dark input[type="text"] {
  color: #ffffff !important;
  background-color: #374151 !important;
}

.dark textarea::placeholder,
.dark input[type="text"]::placeholder {
  color: #9ca3af !important;
}

/* ===== تحسينات RTL للغة العربية ===== */

/* الإعدادات الأساسية لـ RTL */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

/* محاذاة النصوص الأساسية */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, 
[dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
  text-align: right;
}

[dir="rtl"] p, [dir="rtl"] div, [dir="rtl"] span, 
[dir="rtl"] label, [dir="rtl"] button {
  text-align: right;
}

[dir="rtl"] input, [dir="rtl"] textarea, [dir="rtl"] select {
  text-align: right;
  direction: rtl;
}

/* تخطيط Flexbox للعربية */
[dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-between {
  justify-content: space-between;
}

[dir="rtl"] .justify-end {
  justify-content: flex-end;
}

/* إصلاح space-x-reverse للعربية */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* استثناءات للعناصر المركزة */
[dir="rtl"] .text-center,
[dir="rtl"] .justify-center,
[dir="rtl"] .items-center {
  text-align: center;
  justify-content: center;
}

/* استثناءات للكود والأرقام */
[dir="rtl"] code,
[dir="rtl"] pre,
[dir="rtl"] .font-mono,
[dir="rtl"] .text-left-force {
  text-align: left;
  direction: ltr;
}

/* ===== تحسينات خاصة بصفحة الإعدادات ===== */

/* تخطيط بطاقات المقدمين */
[dir="rtl"] .provider-card {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row-reverse;
}

[dir="rtl"] .provider-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-direction: row-reverse;
  text-align: right;
}

[dir="rtl"] .provider-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-direction: row-reverse;
}

/* تحسين النماذج */
[dir="rtl"] .form-grid {
  direction: rtl;
}

[dir="rtl"] .form-field {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .form-label {
  text-align: right;
  direction: rtl;
}

/* تحسين الأزرار */
[dir="rtl"] .btn-rtl {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-direction: row-reverse;
  text-align: right;
}

/* تحسين النماذج المتاحة */
[dir="rtl"] .model-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-direction: row-reverse;
}

[dir="rtl"] .model-info {
  text-align: right;
  direction: rtl;
}

/* تحسين النوافذ المنبثقة */
[dir="rtl"] .modal-content {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .modal-buttons {
  display: flex;
  gap: 0.75rem;
  flex-direction: row-reverse;
}

/* تحسين رسائل الخطأ */
[dir="rtl"] .error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-direction: row-reverse;
  text-align: right;
}

/* ===== تحسينات المسافات ===== */

/* مسافات عامة */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* تحسين الأيقونات */
[dir="rtl"] .icon-before {
  margin-left: 0.5rem;
  margin-right: 0;
}

[dir="rtl"] .icon-after {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* ===== تحسينات responsive ===== */

@media (max-width: 768px) {
  [dir="rtl"] .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  [dir="rtl"] .provider-header {
    flex-direction: column-reverse;
    align-items: flex-end;
    gap: 1rem;
  }
  
  [dir="rtl"] .provider-controls {
    width: 100%;
    justify-content: flex-end;
  }
}

/* ===== إصلاح مشاكل محددة ===== */

/* منع التداخل */
[dir="rtl"] .no-overlap {
  clear: both;
  display: block;
  width: 100%;
}

/* تحسين النصوص الطويلة */
[dir="rtl"] .text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
  text-align: right;
}

/* تحسين المحاذاة القسرية */
[dir="rtl"] .text-right {
  text-align: right !important;
  direction: rtl !important;
}

[dir="rtl"] .align-right {
  text-align: right !important;
  direction: rtl !important;
}
