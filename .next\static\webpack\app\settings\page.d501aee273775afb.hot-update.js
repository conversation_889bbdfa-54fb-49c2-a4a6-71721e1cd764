/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/client-only/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {



/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n__webpack_require__(/*! client-only */ \"(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n_c = React__default;\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    _s();\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\n_s(StyleRegistry, \"vgRS4YV7PcSMQCYHzGaNuBIBcZQ=\");\n_c1 = StyleRegistry;\nfunction useStyleRegistry() {\n    _s1();\n    return React.useContext(StyleSheetContext);\n}\n_s1(useStyleRegistry, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    _s2();\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\n_s2(JSXStyle, \"48Sqj1BUqkshsPdz6NEWXDn8pF4=\", false, function() {\n    return [\n        useStyleRegistry,\n        useInsertionEffect\n    ];\n});\n_c2 = JSXStyle;\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"React__default\");\n$RefreshReg$(_c1, \"StyleRegistry\");\n$RefreshReg$(_c2, \"JSXStyle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/style.js":
/*!******************************************!*\
  !*** ./node_modules/styled-jsx/style.js ***!
  \******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nmodule.exports = __webpack_require__(/*! ./dist/index */ \"(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\").style;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHlsZWQtanN4L3N0eWxlLmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsbUlBQThDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9zdHlsZWQtanN4L3N0eWxlLmpzPzM3MGIiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvaW5kZXgnKS5zdHlsZVxuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIiwic3R5bGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/style.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/llmProviders */ \"(app-pages-browser)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(app-pages-browser)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { currentLanguage, apiSettings, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو الخدمة\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح API\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق...\" : \"Validating...\",\n        valid: isArabic ? \"صالح\" : \"Valid\",\n        invalid: isArabic ? \"غير صالح\" : \"Invalid\",\n        error: isArabic ? \"خطأ\" : \"Error\",\n        models: isArabic ? \"النماذج المتاحة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المحددة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تعديل النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ النماذج\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم تحديد أي نماذج\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة للرئيسية\" : \"Back to Home\",\n        active: isArabic ? \"نشط\" : \"Active\",\n        selectProvider: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\",\n        noProvidersConfigured: isArabic ? \"لم يتم إعداد أي مقدم خدمة بعد\" : \"No providers configured yet\",\n        providerAlreadyExists: isArabic ? \"مقدم الخدمة موجود بالفعل\" : \"Provider already exists\",\n        pleaseSelectProvider: isArabic ? \"يرجى اختيار مقدم خدمة\" : \"Please select a provider\",\n        providerNotFound: isArabic ? \"مقدم الخدمة غير موجود\" : \"Provider not found\",\n        errorAddingProvider: isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(translations.pleaseSelectProvider);\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(translations.providerNotFound);\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(translations.providerAlreadyExists);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(translations.errorAddingProvider);\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        className: \"jsx-1616e681e43fb377\" + \" \" + \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1616e681e43fb377\",\n                children: '[dir=\"rtl\"].jsx-1616e681e43fb377 .text-right.jsx-1616e681e43fb377{text-align:right}[dir=\"rtl\"].jsx-1616e681e43fb377 .provider-card.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .provider-header.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .provider-info.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .provider-controls.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .form-grid.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .form-field.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .form-label.jsx-1616e681e43fb377{text-align:right}[dir=\"rtl\"].jsx-1616e681e43fb377 .model-checkbox.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .model-info.jsx-1616e681e43fb377{direction:rtl;text-align:right}[dir=\"rtl\"].jsx-1616e681e43fb377 .modal-content.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .modal-buttons.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .error-message.jsx-1616e681e43fb377{direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 input[type=\"text\"].jsx-1616e681e43fb377,[dir=\"rtl\"].jsx-1616e681e43fb377 input[type=\"password\"].jsx-1616e681e43fb377,[dir=\"rtl\"].jsx-1616e681e43fb377 select.jsx-1616e681e43fb377,[dir=\"rtl\"].jsx-1616e681e43fb377 textarea.jsx-1616e681e43fb377{text-align:right;direction:rtl}[dir=\"rtl\"].jsx-1616e681e43fb377 .font-arabic.jsx-1616e681e43fb377{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-1616e681e43fb377\" + \" \" + \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1616e681e43fb377\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center justify-between h-16 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 \".concat(isArabic ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-1616e681e43fb377\" + \" \" + \"font-arabic text-right\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1616e681e43fb377\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1616e681e43fb377\" + \" \" + ((isArabic ? \"text-right\" : \"\") || \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white font-arabic text-right\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic text-right\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-1616e681e43fb377\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1616e681e43fb377\" + \" \" + \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1616e681e43fb377\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-1616e681e43fb377\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-1616e681e43fb377\",\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-1616e681e43fb377\" + \" \" + \"text-gray-500 dark:text-gray-400 font-arabic\",\n                                                children: isArabic ? \"لم يتم إعداد أي مقدم خدمة بعد\" : \"No providers configured yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"provider-header flex items-start justify-between mb-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"provider-info flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"provider-controls flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                getStatusIcon((validationState === null || validationState === void 0 ? void 0 : validationState.status) || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 352,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap\",\n                                                                            children: isArabic ? \"نشط\" : \"Active\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"form-grid grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"w-full px-3 py-2 \".concat(isArabic ? \"pr-3 pl-10\" : \"pr-10 pl-3\", \" border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 379,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"absolute \".concat(isArabic ? \"left-3\" : \"right-3\", \" top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"),\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 391,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 391,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 400,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center justify-between w-full mt-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                    children: [\n                                                                        translations.models,\n                                                                        \": \",\n                                                                        providerInfo.models.length\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleValidateProvider(provider.id),\n                                                                    disabled: !provider.apiKey || (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\",\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-1616e681e43fb377\",\n                                                                            children: (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\" ? translations.validating : translations.testConnection\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (validationState === null || validationState === void 0 ? void 0 : validationState.message) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"mt-2 p-2 rounded text-sm \".concat(validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"),\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                                            children: translations.selectedModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setExpandedProviders((prev)=>({\n                                                                                        ...prev,\n                                                                                        [provider.id]: !prev[provider.id]\n                                                                                    }));\n                                                                                if (!selectedModels[provider.id]) {\n                                                                                    setSelectedModels((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: provider.selectedModels || []\n                                                                                        }));\n                                                                                }\n                                                                            },\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic\",\n                                                                            children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 font-arabic\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"flex flex-wrap gap-2\",\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-1616e681e43fb377\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 466,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1616e681e43fb377\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 482,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"model-checkbox flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer \".concat(isArabic ? \"flex-row-reverse space-x-reverse\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 491,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"model-info flex-1 min-w-0\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 498,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 501,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 497,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 487,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 485,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1616e681e43fb377\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"flex gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id),\n                                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 516,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 529,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 524,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1616e681e43fb377\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 537,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"flex flex-wrap gap-2\",\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"font-arabic\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 546,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 551,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 547,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 542,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 540,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1616e681e43fb377\" + \" \" + \"flex justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"jsx-1616e681e43fb377\" + \" \" + \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 561,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-1616e681e43fb377\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    className: \"jsx-1616e681e43fb377\" + \" \" + \"modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-1616e681e43fb377\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1616e681e43fb377\" + \" \" + \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                className: \"jsx-1616e681e43fb377\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        className: \"jsx-1616e681e43fb377\",\n                                        children: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            className: \"jsx-1616e681e43fb377\",\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1616e681e43fb377\" + \" \" + \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-1616e681e43fb377\" + \" \" + \"error-message flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-1616e681e43fb377\" + \" \" + \"text-sm text-red-700 dark:text-red-300 font-arabic\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1616e681e43fb377\" + \" \" + \"modal-buttons flex gap-3 mt-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic\",\n                                    children: isArabic ? \"إلغاء\" : \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"jsx-1616e681e43fb377\" + \" \" + \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic\",\n                                    children: isArabic ? \"إضافة\" : \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 586,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"E2yalVrLtfODjnaMswjgodk23ig=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});