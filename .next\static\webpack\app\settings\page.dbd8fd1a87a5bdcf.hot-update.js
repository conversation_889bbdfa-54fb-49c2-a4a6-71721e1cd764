"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/llmProviders */ \"(app-pages-browser)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(app-pages-browser)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { currentLanguage, apiSettings, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو الخدمة\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح API\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق...\" : \"Validating...\",\n        valid: isArabic ? \"صالح\" : \"Valid\",\n        invalid: isArabic ? \"غير صالح\" : \"Invalid\",\n        error: isArabic ? \"خطأ\" : \"Error\",\n        models: isArabic ? \"النماذج المتاحة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المحددة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تعديل النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ النماذج\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم تحديد أي نماذج\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة للرئيسية\" : \"Back to Home\",\n        active: isArabic ? \"نشط\" : \"Active\",\n        selectProvider: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\",\n        noProvidersConfigured: isArabic ? \"لم يتم إعداد أي مقدم خدمة بعد\" : \"No providers configured yet\",\n        providerAlreadyExists: isArabic ? \"مقدم الخدمة موجود بالفعل\" : \"Provider already exists\",\n        pleaseSelectProvider: isArabic ? \"يرجى اختيار مقدم خدمة\" : \"Please select a provider\",\n        providerNotFound: isArabic ? \"مقدم الخدمة غير موجود\" : \"Provider not found\",\n        errorAddingProvider: isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(translations.pleaseSelectProvider);\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(translations.providerNotFound);\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(translations.providerAlreadyExists);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(translations.errorAddingProvider);\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"a088ed7465bc8f86\",\n                children: '[dir=\"rtl\"].jsx-a088ed7465bc8f86 *.jsx-a088ed7465bc8f86{font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-a088ed7465bc8f86 .text-right.jsx-a088ed7465bc8f86{text-align:right!important}[dir=\"rtl\"].jsx-a088ed7465bc8f86 .text-left.jsx-a088ed7465bc8f86{text-align:left!important}[dir=\"rtl\"].jsx-a088ed7465bc8f86 input[type=\"text\"].jsx-a088ed7465bc8f86,[dir=\"rtl\"].jsx-a088ed7465bc8f86 input[type=\"password\"].jsx-a088ed7465bc8f86,[dir=\"rtl\"].jsx-a088ed7465bc8f86 select.jsx-a088ed7465bc8f86,[dir=\"rtl\"].jsx-a088ed7465bc8f86 textarea.jsx-a088ed7465bc8f86{text-align:right!important;direction:rtl!important;font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-a088ed7465bc8f86 .font-arabic.jsx-a088ed7465bc8f86{font-family:\"Tajawal\",\"Arial\",sans-serif!important;text-align:right!important}[dir=\"rtl\"].jsx-a088ed7465bc8f86 .icon-before-text.jsx-a088ed7465bc8f86{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center justify-between h-16 w-full \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 \".concat(isArabic ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a088ed7465bc8f86\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center justify-between w-full \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-a088ed7465bc8f86\",\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-gray-500 dark:text-gray-400 font-arabic text-right\",\n                                                children: translations.noProvidersConfigured\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-start justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-4 \".concat(isArabic ? \"icon-before-text\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-3\",\n                                                            children: [\n                                                                getStatusIcon((validationState === null || validationState === void 0 ? void 0 : validationState.status) || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-2 \".concat(isArabic ? \"icon-before-text\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                            children: translations.active\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"form-grid grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-right\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"w-full px-3 py-2 \".concat(isArabic ? \"pr-3 pl-10 text-right\" : \"pr-10 pl-3\", \" border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 389,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"absolute \".concat(isArabic ? \"left-3\" : \"right-3\", \" top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"),\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 401,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 401,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-right\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(isArabic ? \"text-right\" : \"\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center justify-between w-full mt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                    children: [\n                                                                        translations.models,\n                                                                        \": \",\n                                                                        providerInfo.models.length\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleValidateProvider(provider.id),\n                                                                    disabled: !provider.apiKey || (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\",\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic \".concat(isArabic ? \"icon-before-text\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\",\n                                                                            children: (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\" ? translations.validating : translations.testConnection\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 432,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (validationState === null || validationState === void 0 ? void 0 : validationState.message) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"mt-2 p-2 rounded text-sm \".concat(validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"),\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                            children: translations.selectedModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setExpandedProviders((prev)=>({\n                                                                                        ...prev,\n                                                                                        [provider.id]: !prev[provider.id]\n                                                                                    }));\n                                                                                if (!selectedModels[provider.id]) {\n                                                                                    setSelectedModels((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: provider.selectedModels || []\n                                                                                        }));\n                                                                                }\n                                                                            },\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                            children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex flex-wrap gap-2 \".concat(isArabic ? \"justify-end\" : \"justify-start\"),\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 492,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer \".concat(isArabic ? \"icon-before-text\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 501,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex-1 min-w-0\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 508,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 511,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 507,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 497,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 495,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 522,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id),\n                                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 526,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm flex-shrink-0\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 540,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 535,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 548,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex flex-wrap gap-2 \".concat(isArabic ? \"justify-end\" : \"justify-start\"),\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 557,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400 flex-shrink-0\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 562,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 558,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 553,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex \".concat(isArabic ? \"justify-start\" : \"justify-end\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        className: \"jsx-a088ed7465bc8f86\",\n                                        children: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            className: \"jsx-a088ed7465bc8f86\",\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex items-center gap-2 \".concat(isArabic ? \"icon-before-text\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-a088ed7465bc8f86\" + \" \" + \"text-sm text-red-700 dark:text-red-300 font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex gap-3 mt-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                    children: isArabic ? \"إلغاء\" : \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"jsx-a088ed7465bc8f86\" + \" \" + \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                    children: isArabic ? \"إضافة\" : \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 597,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"E2yalVrLtfODjnaMswjgodk23ig=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});