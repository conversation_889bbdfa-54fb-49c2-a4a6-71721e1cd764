'use client';

import { useState, useEffect } from 'react';
import { useContextStore } from '@/store/contextStore';
import {
  <PERSON>rkles,
  CheckCircle,
  XCircle,
  Loader2,
  Zap,
  Brain,
  Activity,
  Server,
  Key,
  Database
} from 'lucide-react';

export default function TestAIGeneration() {
  const { getActiveProviders, currentLanguage } = useContextStore();
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const isArabic = currentLanguage === 'ar';

  // Only calculate after mounting to prevent hydration issues
  const activeProviders = mounted ? getActiveProviders() : [];
  const hasValidProvider = mounted ? activeProviders.some(p =>
    p.apiKey &&
    p.validationStatus === 'valid' &&
    p.selectedModels &&
    p.selectedModels.length > 0
  ) : false;

  const testAIGeneration = async () => {
    if (!hasValidProvider) {
      setTestResult(isArabic ? 'لا يوجد مقدم خدمة صالح' : 'No valid provider found');
      return;
    }

    setIsLoading(true);
    setTestResult('');

    try {
      const provider = activeProviders.find(p => p.apiKey && p.validationStatus === 'valid');
      
      if (!provider) {
        throw new Error('No valid provider found');
      }

      console.log('Testing with provider:', provider);

      const response = await fetch('/api/llm/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          providerId: provider.id,
          apiKey: provider.apiKey,
          model: provider.selectedModels[0] || 'gpt-3.5-turbo',
          messages: [
            { 
              role: 'user', 
              content: isArabic 
                ? 'اقترح 3 أسماء إبداعية لمشروع تطبيق جوال للتسوق الإلكتروني'
                : 'Suggest 3 creative names for a mobile e-commerce app project'
            }
          ],
          context: { test: true },
          fieldName: 'test',
          language: currentLanguage,
          temperature: 0.8,
          maxTokens: 500
        })
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('API Result:', result);

      if (result.success) {
        setTestResult(result.content);
      } else {
        setTestResult(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Test error:', error);
      setTestResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900 rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-8" dir={isArabic ? 'rtl' : 'ltr'}>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-indigo-400/10 dark:from-blue-600/10 dark:via-purple-600/10 dark:to-indigo-600/10"></div>
        <div className="relative">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white font-arabic">
              {isArabic ? 'اختبار توليد الذكاء الاصطناعي' : 'AI Generation Test'}
            </h3>
          </div>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-1/2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <style jsx>{`
        /* تحسينات CSS للمكون */
        [dir="rtl"] .text-content {
          text-align: right;
          direction: rtl;
        }

        [dir="rtl"] .font-arabic {
          font-family: 'Tajawal', 'Arial', sans-serif;
          text-align: right;
        }

        /* تأثيرات الحركة */
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        .float-animation {
          animation: float 6s ease-in-out infinite;
        }

        /* تأثير الشرارات */
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0); }
          50% { opacity: 1; transform: scale(1); }
        }

        .sparkle {
          animation: sparkle 2s ease-in-out infinite;
        }

        /* تأثير النبض المتقدم */
        @keyframes advanced-pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.05);
          }
        }

        .advanced-pulse {
          animation: advanced-pulse 2s ease-in-out infinite;
        }
      `}</style>

      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900 rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-8" dir={isArabic ? 'rtl' : 'ltr'} suppressHydrationWarning>
      {/* خلفية متحركة */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-indigo-400/10 dark:from-blue-600/10 dark:via-purple-600/10 dark:to-indigo-600/10"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-indigo-400/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-400/20 to-purple-400/20 rounded-full blur-2xl"></div>

      <div className="relative">
        {/* العنوان */}
        <div className={`flex items-center gap-4 mb-8 ${isArabic ? 'flex-row-reverse' : ''}`}>
          <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <div className="text-content">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white font-arabic">
              {isArabic ? 'اختبار توليد الذكاء الاصطناعي' : 'AI Generation Test'}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
              {isArabic ? 'تحقق من حالة الاتصال مع نماذج الذكاء الاصطناعي' : 'Check connection status with AI models'}
            </p>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Server className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-content">
                <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                  {isArabic ? 'مقدمو الخدمة النشطون' : 'Active Providers'}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white" suppressHydrationWarning>
                  {activeProviders.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <div className={`p-2 rounded-lg ${hasValidProvider ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'}`}>
                {hasValidProvider ? (
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                )}
              </div>
              <div className="text-content">
                <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                  {isArabic ? 'مقدم خدمة صالح' : 'Valid Provider'}
                </p>
                <p className={`text-2xl font-bold ${hasValidProvider ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`} suppressHydrationWarning>
                  {hasValidProvider ? '✅' : '❌'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* تفاصيل مقدمي الخدمة */}
        <div className="space-y-3 mb-8">
          {activeProviders.map(provider => (
            <div key={provider.id} className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50 hover:shadow-lg transition-all duration-300">
              <div className={`flex items-start justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
                  <div className={`p-2 rounded-lg ${provider.validationStatus === 'valid' ? 'bg-green-100 dark:bg-green-900/30' : 'bg-gray-100 dark:bg-gray-700'}`}>
                    <Activity className={`w-4 h-4 ${provider.validationStatus === 'valid' ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}`} />
                  </div>
                  <div className="text-content">
                    <h4 className="font-semibold text-gray-900 dark:text-white font-arabic">
                      {provider.id}
                    </h4>
                    <p className={`text-xs font-arabic ${provider.validationStatus === 'valid' ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}`}>
                      {provider.validationStatus}
                    </p>
                  </div>
                </div>

                <div className={`flex items-center gap-4 text-xs ${isArabic ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center gap-1 ${isArabic ? 'flex-row-reverse' : ''}`}>
                    <Key className="w-3 h-3" />
                    <span className="font-arabic text-content">
                      {isArabic ? 'مفتاح API:' : 'API Key:'} {provider.apiKey ? '✅' : '❌'}
                    </span>
                  </div>
                  <div className={`flex items-center gap-1 ${isArabic ? 'flex-row-reverse' : ''}`}>
                    <Database className="w-3 h-3" />
                    <span className="font-arabic text-content">
                      {isArabic ? 'النماذج المختارة:' : 'Selected Models:'} {provider.selectedModels?.length || 0}
                    </span>
                  </div>
                </div>
              </div>

              {provider.selectedModels && provider.selectedModels.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                  <div className={`flex flex-wrap gap-2 ${isArabic ? 'justify-end' : 'justify-start'}`}>
                    {provider.selectedModels.map((model, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg text-xs font-arabic">
                        {model}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* زر الاختبار */}
        <div className={`flex ${isArabic ? 'justify-start' : 'justify-center'} mb-8`}>
          <button
            onClick={testAIGeneration}
            disabled={isLoading || !hasValidProvider}
            className={`relative group px-8 py-4 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700 text-white font-semibold rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 active:scale-95 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none overflow-hidden ${isArabic ? 'flex-row-reverse' : ''}`}
          >
            {/* تأثير الإضاءة */}
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            {/* المحتوى */}
            <div className={`relative flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              {isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Zap className="w-5 h-5 group-hover:animate-pulse" />
              )}
              <span className="font-arabic text-content">
                {isLoading
                  ? (isArabic ? 'جاري الاختبار...' : 'Testing...')
                  : (isArabic ? 'اختبار التوليد' : 'Test Generation')
                }
              </span>
            </div>

            {/* تأثير الشرارات */}
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-2 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping"></div>
              <div className="absolute top-4 right-6 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping" style={{animationDelay: '0.2s'}}></div>
              <div className="absolute bottom-3 left-8 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping" style={{animationDelay: '0.4s'}}></div>
            </div>
          </button>
        </div>

        {/* النتائج */}
        {testResult && (
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
            <div className={`flex items-center gap-3 mb-4 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Sparkles className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content">
                {isArabic ? 'نتيجة الاختبار' : 'Test Result'}
              </h4>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200/30 dark:border-gray-700/30">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap font-arabic text-content leading-relaxed">
                {testResult}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  );
}
