'use client';

import { useContextStore } from '@/store/contextStore';
import {
  TrendingUp,
  Target,
  CheckCircle,
  Clock,
  Zap,
  BarChart3,
  <PERSON>,
  <PERSON>rk<PERSON>,
  <PERSON>,
  Rocket
} from 'lucide-react';

interface ProgressIndicatorProps {
  currentModule?: string;
}

export default function ProgressIndicator({ currentModule }: ProgressIndicatorProps) {
  const { 
    projectDefinition, 
    contextMap, 
    emotionalTone, 
    technicalLayer, 
    legalRisk,
    currentLanguage 
  } = useContextStore();
  
  const isArabic = currentLanguage === 'ar';

  const modules = [
    {
      key: 'project-definition',
      name: 'Project Definition',
      nameAr: 'تعريف المشروع',
      emoji: '🎯',
      data: projectDefinition,
      href: '/project-definition'
    },
    {
      key: 'context-map',
      name: 'Context Map',
      nameAr: 'خريطة السياق',
      emoji: '🗺️',
      data: contextMap,
      href: '/context-map'
    },
    {
      key: 'emotional-tone',
      name: 'Emotional Tone',
      nameAr: 'النبرة العاطفية',
      emoji: '✨',
      data: emotionalTone,
      href: '/emotional-tone'
    },
    {
      key: 'technical-layer',
      name: 'Technical Layer',
      nameAr: 'الطبقة التقنية',
      emoji: '⚙️',
      data: technicalLayer,
      href: '/technical-layer'
    },
    {
      key: 'legal-risk',
      name: 'Legal & Privacy',
      nameAr: 'القانونية والخصوصية',
      emoji: '🔒',
      data: legalRisk,
      href: '/legal-risk'
    }
  ];

  const getModuleProgress = (moduleData: any) => {
    const totalFields = Object.keys(moduleData).length;
    const filledFields = Object.values(moduleData).filter(value => 
      value && typeof value === 'string' && value.trim()
    ).length;
    return totalFields > 0 ? (filledFields / totalFields) * 100 : 0;
  };

  const overallProgress = modules.reduce((total, module) => {
    return total + getModuleProgress(module.data);
  }, 0) / modules.length;

  // تحليل ذكي للتقدم
  const getProgressInsights = () => {
    const completedModules = modules.filter(m => getModuleProgress(m.data) === 100).length;
    const inProgressModules = modules.filter(m => {
      const progress = getModuleProgress(m.data);
      return progress > 0 && progress < 100;
    }).length;
    const notStartedModules = modules.filter(m => getModuleProgress(m.data) === 0).length;

    return {
      completed: completedModules,
      inProgress: inProgressModules,
      notStarted: notStartedModules,
      totalFields: modules.reduce((total, m) => total + Object.keys(m.data).length, 0),
      filledFields: modules.reduce((total, m) => {
        return total + Object.values(m.data).filter(v => v && typeof v === 'string' && v.trim()).length;
      }, 0)
    };
  };

  const insights = getProgressInsights();
  const progressLevel = overallProgress >= 80 ? 'excellent' : overallProgress >= 60 ? 'good' : overallProgress >= 30 ? 'fair' : 'start';

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900 rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-8 mb-8" dir={isArabic ? 'rtl' : 'ltr'}>
      {/* خلفية متحركة */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/5 via-purple-400/5 to-indigo-400/5 dark:from-blue-600/5 dark:via-purple-600/5 dark:to-indigo-600/5"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-indigo-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>

      <div className="relative">
        {/* رأس القسم مع تحليل ذكي */}
        <div className={`flex items-center justify-between mb-8 ${isArabic ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-4 ${isArabic ? 'flex-row-reverse' : ''}`}>
            <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white font-arabic">
                {isArabic ? 'تحليل تقدم المشروع' : 'Project Progress Analytics'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                {isArabic ? 'تحليل ذكي شامل لحالة مشروعك' : 'Comprehensive smart analysis of your project status'}
              </p>
            </div>
          </div>

          <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
            <div className="text-right">
              <div className="text-3xl font-bold text-transparent bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text">
                {Math.round(overallProgress)}%
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 font-arabic">
                {isArabic ? 'مكتمل' : 'Complete'}
              </div>
            </div>
            {progressLevel === 'excellent' && <Award className="w-8 h-8 text-yellow-500" />}
            {progressLevel === 'good' && <Target className="w-8 h-8 text-green-500" />}
            {progressLevel === 'fair' && <TrendingUp className="w-8 h-8 text-blue-500" />}
            {progressLevel === 'start' && <Rocket className="w-8 h-8 text-purple-500" />}
          </div>
        </div>

        {/* شريط التقدم الرئيسي المحسن */}
        <div className="relative mb-8">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 shadow-inner">
            <div
              className={`h-4 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r ${
                progressLevel === 'excellent' ? 'from-green-400 to-emerald-500' :
                progressLevel === 'good' ? 'from-blue-400 to-indigo-500' :
                progressLevel === 'fair' ? 'from-yellow-400 to-orange-500' :
                'from-purple-400 to-pink-500'
              } shadow-lg relative overflow-hidden`}
              style={{ width: `${overallProgress}%` }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent animate-pulse"></div>
            </div>
          </div>
          <div className={`absolute top-5 ${isArabic ? 'right-0' : 'left-0'} text-xs text-gray-600 dark:text-gray-400 font-arabic`}>
            {insights.filledFields} / {insights.totalFields} {isArabic ? 'حقل مكتمل' : 'fields completed'}
          </div>
        </div>

        {/* إحصائيات ذكية */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{insights.completed}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400 font-arabic">
                  {isArabic ? 'محاور مكتملة' : 'Completed Modules'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{insights.inProgress}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400 font-arabic">
                  {isArabic ? 'قيد التطوير' : 'In Progress'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Brain className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{insights.notStarted}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400 font-arabic">
                  {isArabic ? 'لم تبدأ بعد' : 'Not Started'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* محاور المشروع المحسنة */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          {modules.map((module) => {
            const progress = getModuleProgress(module.data);
            const isCurrent = currentModule === module.key;

            return (
              <a
                key={module.key}
                href={module.href}
                className={`group relative p-4 rounded-xl border-2 transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                  isCurrent
                    ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 shadow-lg'
                    : 'border-gray-200/50 dark:border-gray-600/50 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm hover:border-blue-300 dark:hover:border-blue-500'
                }`}
              >
                <div className="text-center">
                  <div className="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">{module.emoji}</div>
                  <div className="text-xs font-medium text-gray-900 dark:text-white mb-3 font-arabic">
                    {isArabic ? module.nameAr : module.name}
                  </div>

                  {/* شريط تقدم دائري */}
                  <div className="relative w-12 h-12 mx-auto mb-2">
                    <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        className="text-gray-200 dark:text-gray-700"
                      />
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeDasharray={`${progress}, 100`}
                        className={`transition-all duration-1000 ${
                          progress === 100 ? 'text-green-500' : 'text-blue-500'
                        }`}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-xs font-bold text-gray-700 dark:text-gray-300">
                        {Math.round(progress)}%
                      </span>
                    </div>
                  </div>

                  {progress === 100 && (
                    <div className="flex justify-center">
                      <Sparkles className="w-4 h-4 text-green-500 animate-pulse" />
                    </div>
                  )}
                </div>
              </a>
            );
          })}
        </div>

        {/* أزرار العمل السريع */}
        <div className={`flex justify-center gap-4 ${isArabic ? 'flex-row-reverse' : ''}`}>
          <a
            href="/final-preview"
            className={`group relative flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 transition-all duration-300 overflow-hidden ${isArabic ? 'flex-row-reverse' : ''}`}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative flex items-center gap-3">
              <Zap className="w-5 h-5 group-hover:animate-pulse" />
              <span className="font-arabic">
                {isArabic ? 'المعاينة النهائية' : 'Final Preview'}
              </span>
            </div>
          </a>

          {overallProgress > 0 && (
            <button
              onClick={() => {
                if (confirm(isArabic ? 'هل أنت متأكد من إعادة تعيين جميع البيانات؟' : 'Are you sure you want to reset all data?')) {
                  window.location.reload();
                }
              }}
              className={`group relative flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 transition-all duration-300 overflow-hidden ${isArabic ? 'flex-row-reverse' : ''}`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative flex items-center gap-3">
                <Rocket className="w-5 h-5 group-hover:animate-spin" />
                <span className="font-arabic">
                  {isArabic ? 'إعادة تعيين' : 'Reset'}
                </span>
              </div>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
