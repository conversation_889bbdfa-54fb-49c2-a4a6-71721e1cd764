globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/technical-layer/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/context-map/page.tsx":{"*":{"id":"(ssr)/./src/app/context-map/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/StoreHydration.tsx":{"*":{"id":"(ssr)/./src/components/StoreHydration.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ThemeProvider.tsx":{"*":{"id":"(ssr)/./src/components/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/technical-layer/page.tsx":{"*":{"id":"(ssr)/./src/app/technical-layer/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/final-preview/page.tsx":{"*":{"id":"(ssr)/./src/app/final-preview/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/legal-risk/page.tsx":{"*":{"id":"(ssr)/./src/app/legal-risk/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx":{"id":"(app-pages-browser)/./src/app/context-map/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\StoreHydration.tsx":{"id":"(app-pages-browser)/./src/components/StoreHydration.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx":{"id":"(app-pages-browser)/./src/components/ThemeProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\styles\\glass-effects.css":{"id":"(app-pages-browser)/./src/styles/glass-effects.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\styles\\text-improvements.css":{"id":"(app-pages-browser)/./src/styles/text-improvements.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx":{"id":"(app-pages-browser)/./src/app/technical-layer/page.tsx","name":"*","chunks":["app/technical-layer/page","static/chunks/app/technical-layer/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx":{"id":"(app-pages-browser)/./src/app/final-preview/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx":{"id":"(app-pages-browser)/./src/app/legal-risk/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\":[],"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page":[]}}