"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/llmProviders */ \"(app-pages-browser)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(app-pages-browser)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { currentLanguage, apiSettings, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو الخدمة\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح API\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق...\" : \"Validating...\",\n        valid: isArabic ? \"صالح\" : \"Valid\",\n        invalid: isArabic ? \"غير صالح\" : \"Invalid\",\n        error: isArabic ? \"خطأ\" : \"Error\",\n        models: isArabic ? \"النماذج المتاحة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المحددة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تعديل النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ النماذج\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم تحديد أي نماذج\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة للرئيسية\" : \"Back to Home\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(isArabic ? \"يرجى اختيار مقدم خدمة\" : \"Please select a provider\");\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة غير موجود\" : \"Provider not found\");\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة موجود بالفعل\" : \"Provider already exists\");\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\");\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 \".concat(isArabic ? \"rotate-180\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-arabic\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 font-arabic\",\n                                                children: isArabic ? \"لم يتم إعداد أي مقدم خدمة بعد\" : \"No providers configured yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"provider-header flex items-start justify-between mb-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"provider-info flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"provider-controls flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                getStatusIcon((validationState === null || validationState === void 0 ? void 0 : validationState.status) || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap\",\n                                                                            children: isArabic ? \"نشط\" : \"Active\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    className: \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-grid grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    className: \"w-full px-3 py-2 \".concat(isArabic ? \"pr-3 pl-10\" : \"pr-10 pl-3\", \" border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"),\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 348,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    className: \"absolute \".concat(isArabic ? \"left-3\" : \"right-3\", \" top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"),\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 360,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 360,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 356,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"form-field space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full mt-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                    children: [\n                                                                        translations.models,\n                                                                        \": \",\n                                                                        providerInfo.models.length\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleValidateProvider(provider.id),\n                                                                    disabled: !provider.apiKey || (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\",\n                                                                    className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\" ? translations.validating : translations.testConnection\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (validationState === null || validationState === void 0 ? void 0 : validationState.message) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-2 rounded text-sm \".concat(validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"),\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                                            children: translations.selectedModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setExpandedProviders((prev)=>({\n                                                                                        ...prev,\n                                                                                        [provider.id]: !prev[provider.id]\n                                                                                    }));\n                                                                                if (!selectedModels[provider.id]) {\n                                                                                    setSelectedModels((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: provider.selectedModels || []\n                                                                                        }));\n                                                                                }\n                                                                            },\n                                                                            className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic\",\n                                                                            children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500 dark:text-gray-400 font-arabic\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-2\",\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"model-checkbox flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer \".concat(isArabic ? \"flex-row-reverse space-x-reverse\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 460,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"model-info flex-1 min-w-0\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 467,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 470,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 466,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 456,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 454,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 481,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\",\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 485,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            className: \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 498,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 493,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 484,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-2\",\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-arabic\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 515,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    className: \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 520,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 516,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 511,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic\",\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error-message flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-700 dark:text-red-300 font-arabic\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-buttons flex gap-3 mt-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic\",\n                                    children: isArabic ? \"إلغاء\" : \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic\",\n                                    children: isArabic ? \"إضافة\" : \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 556,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 555,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"E2yalVrLtfODjnaMswjgodk23ig=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});