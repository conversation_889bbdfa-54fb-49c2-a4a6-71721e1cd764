"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/llm/validate/route";
exports.ids = ["app/api/llm/validate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fvalidate%2Froute&page=%2Fapi%2Fllm%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fvalidate%2Froute&page=%2Fapi%2Fllm%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_ContextKit_src_app_api_llm_validate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/llm/validate/route.ts */ \"(rsc)/./src/app/api/llm/validate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/llm/validate/route\",\n        pathname: \"/api/llm/validate\",\n        filename: \"route\",\n        bundlePath: \"app/api/llm/validate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\api\\\\llm\\\\validate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_ContextKit_src_app_api_llm_validate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/llm/validate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fvalidate%2Froute&page=%2Fapi%2Fllm%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/llm/validate/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/llm/validate/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/llmProviders */ \"(rsc)/./src/lib/llmProviders.ts\");\n\n\n/**\n * API للتحقق من صحة مفاتيح API لمقدمي خدمات LLM\n */ async function POST(request) {\n    try {\n        const { providerId, apiKey, baseUrl } = await request.json();\n        if (!providerId || !apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: \"Provider ID and API key are required\"\n            }, {\n                status: 400\n            });\n        }\n        const provider = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderById)(providerId);\n        if (!provider) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                valid: false,\n                error: \"Unknown provider\"\n            }, {\n                status: 400\n            });\n        }\n        const finalBaseUrl = baseUrl || provider.baseUrl;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...(0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderHeaders)(providerId)\n        };\n        let validationResult;\n        switch(providerId){\n            case \"openai\":\n                validationResult = await validateOpenAI(apiKey, finalBaseUrl, headers);\n                break;\n            case \"anthropic\":\n                validationResult = await validateAnthropic(apiKey, finalBaseUrl, headers);\n                break;\n            case \"google\":\n                validationResult = await validateGoogle(apiKey, finalBaseUrl, headers);\n                break;\n            case \"openrouter\":\n                validationResult = await validateOpenRouter(apiKey, finalBaseUrl, headers);\n                break;\n            case \"deepseek\":\n                validationResult = await validateDeepSeek(apiKey, finalBaseUrl, headers);\n                break;\n            case \"groq\":\n                validationResult = await validateGroq(apiKey, finalBaseUrl, headers);\n                break;\n            default:\n                validationResult = await validateGeneric(apiKey, finalBaseUrl, headers);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(validationResult);\n    } catch (error) {\n        console.error(\"Validation error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            valid: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function validateOpenAI(apiKey, baseUrl, headers) {\n    try {\n        const response = await fetch(`${baseUrl}/models`, {\n            method: \"GET\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                valid: true,\n                models: data.data?.map((model)=>model.id) || [],\n                message: \"API key is valid\"\n            };\n        } else {\n            const error = await response.text();\n            return {\n                valid: false,\n                error: `OpenAI API error: ${response.status} - ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            valid: false,\n            error: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n        };\n    }\n}\nasync function validateAnthropic(apiKey, baseUrl, headers) {\n    try {\n        // Anthropic doesn't have a models endpoint, so we test with a simple completion\n        const response = await fetch(`${baseUrl}/messages`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"x-api-key\": apiKey\n            },\n            body: JSON.stringify({\n                model: \"claude-3-5-haiku-20241022\",\n                max_tokens: 1,\n                messages: [\n                    {\n                        role: \"user\",\n                        content: \"Hi\"\n                    }\n                ]\n            })\n        });\n        if (response.ok || response.status === 400) {\n            // 400 is expected for minimal request, but means API key is valid\n            return {\n                valid: true,\n                message: \"API key is valid\"\n            };\n        } else {\n            const error = await response.text();\n            return {\n                valid: false,\n                error: `Anthropic API error: ${response.status} - ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            valid: false,\n            error: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n        };\n    }\n}\nasync function validateGoogle(apiKey, baseUrl, headers) {\n    try {\n        const response = await fetch(`${baseUrl}/models?key=${apiKey}`, {\n            method: \"GET\",\n            headers\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                valid: true,\n                models: data.models?.map((model)=>model.name.replace(\"models/\", \"\")) || [],\n                message: \"API key is valid\"\n            };\n        } else {\n            const error = await response.text();\n            return {\n                valid: false,\n                error: `Google API error: ${response.status} - ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            valid: false,\n            error: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n        };\n    }\n}\nasync function validateOpenRouter(apiKey, baseUrl, headers) {\n    try {\n        const response = await fetch(`${baseUrl}/models`, {\n            method: \"GET\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                valid: true,\n                models: data.data?.map((model)=>model.id) || [],\n                message: \"API key is valid\"\n            };\n        } else {\n            const error = await response.text();\n            return {\n                valid: false,\n                error: `OpenRouter API error: ${response.status} - ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            valid: false,\n            error: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n        };\n    }\n}\nasync function validateDeepSeek(apiKey, baseUrl, headers) {\n    try {\n        const response = await fetch(`${baseUrl}/models`, {\n            method: \"GET\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                valid: true,\n                models: data.data?.map((model)=>model.id) || [],\n                message: \"API key is valid\"\n            };\n        } else {\n            const error = await response.text();\n            return {\n                valid: false,\n                error: `DeepSeek API error: ${response.status} - ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            valid: false,\n            error: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n        };\n    }\n}\nasync function validateGroq(apiKey, baseUrl, headers) {\n    try {\n        const response = await fetch(`${baseUrl}/models`, {\n            method: \"GET\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                valid: true,\n                models: data.data?.map((model)=>model.id) || [],\n                message: \"API key is valid\"\n            };\n        } else {\n            const error = await response.text();\n            return {\n                valid: false,\n                error: `Groq API error: ${response.status} - ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            valid: false,\n            error: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n        };\n    }\n}\nasync function validateGeneric(apiKey, baseUrl, headers) {\n    try {\n        // Try common endpoints\n        const endpoints = [\n            \"/models\",\n            \"/v1/models\"\n        ];\n        for (const endpoint of endpoints){\n            try {\n                const response = await fetch(`${baseUrl}${endpoint}`, {\n                    method: \"GET\",\n                    headers: {\n                        ...headers,\n                        \"Authorization\": `Bearer ${apiKey}`\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    return {\n                        valid: true,\n                        models: data.data?.map((model)=>model.id) || [],\n                        message: \"API key is valid\"\n                    };\n                }\n            } catch (error) {\n                continue;\n            }\n        }\n        return {\n            valid: false,\n            error: \"Could not validate API key with standard endpoints\"\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/llm/validate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model with vision capabilities\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable version of GPT-4o\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model with latest knowledge\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-4\",\n                name: \"GPT-4\",\n                description: \"Original GPT-4 model with strong reasoning\",\n                contextLength: 8192,\n                pricing: \"$30/1M input, $60/1M output\",\n                inputPrice: 30,\n                outputPrice: 60\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient for most tasks\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gpt-3.5-turbo-instruct\",\n                name: \"GPT-3.5 Turbo Instruct\",\n                description: \"Instruction-following variant\",\n                contextLength: 4096,\n                pricing: \"$1.5/1M input, $2/1M output\",\n                inputPrice: 1.5,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Most advanced model with 2M context window\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient with 1M context\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-1.5-flash-8b\",\n                name: \"Gemini 1.5 Flash 8B\",\n                description: \"Smaller, faster model for simple tasks\",\n                contextLength: 1000000,\n                pricing: \"$0.0375/1M input, $0.15/1M output\",\n                inputPrice: 0.0375,\n                outputPrice: 0.15\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance for general use\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gemini-pro-vision\",\n                name: \"Gemini Pro Vision\",\n                description: \"Multimodal model with vision capabilities\",\n                contextLength: 16000,\n                pricing: \"$0.25/1M input, $0.5/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"mistral\",\n        name: \"Mistral AI\",\n        icon: \"\\uD83C\\uDF1F\",\n        description: \"Mistral AI - Advanced European AI models with multilingual capabilities\",\n        baseUrl: \"https://api.mistral.ai/v1\",\n        apiKeyPlaceholder: \"mistral_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"mistral-large-latest\",\n                name: \"Mistral Large\",\n                description: \"Most advanced model with superior reasoning\",\n                contextLength: 128000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-medium-latest\",\n                name: \"Mistral Medium\",\n                description: \"Balanced performance and cost\",\n                contextLength: 32000,\n                pricing: \"$2.7/1M input, $8.1/1M output\",\n                inputPrice: 2.7,\n                outputPrice: 8.1\n            },\n            {\n                id: \"mistral-small-latest\",\n                name: \"Mistral Small\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 32000,\n                pricing: \"$0.2/1M input, $0.6/1M output\",\n                inputPrice: 0.2,\n                outputPrice: 0.6\n            },\n            {\n                id: \"open-mistral-7b\",\n                name: \"Open Mistral 7B\",\n                description: \"Open source model, fast and efficient\",\n                contextLength: 32000,\n                pricing: \"$0.25/1M input, $0.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.25\n            },\n            {\n                id: \"open-mixtral-8x7b\",\n                name: \"Open Mixtral 8x7B\",\n                description: \"Mixture of experts model\",\n                contextLength: 32000,\n                pricing: \"$0.7/1M input, $0.7/1M output\",\n                inputPrice: 0.7,\n                outputPrice: 0.7\n            },\n            {\n                id: \"open-mixtral-8x22b\",\n                name: \"Open Mixtral 8x22B\",\n                description: \"Larger mixture of experts model\",\n                contextLength: 64000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-embed\",\n                name: \"Mistral Embed\",\n                description: \"Embedding model for semantic search\",\n                contextLength: 8192,\n                pricing: \"$0.1/1M tokens\",\n                inputPrice: 0.1,\n                outputPrice: 0\n            }\n        ]\n    },\n    {\n        id: \"cohere\",\n        name: \"Cohere\",\n        icon: \"\\uD83E\\uDDEE\",\n        description: \"Cohere - Enterprise-grade language models with strong multilingual support\",\n        baseUrl: \"https://api.cohere.ai/v1\",\n        apiKeyPlaceholder: \"co_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"command-r-plus\",\n                name: \"Command R+\",\n                description: \"Most advanced model for complex reasoning and RAG\",\n                contextLength: 128000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"command-r\",\n                name: \"Command R\",\n                description: \"Balanced model for general use and RAG\",\n                contextLength: 128000,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"command\",\n                name: \"Command\",\n                description: \"Versatile model for various tasks\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            },\n            {\n                id: \"command-light\",\n                name: \"Command Light\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 4096,\n                pricing: \"$0.3/1M input, $0.6/1M output\",\n                inputPrice: 0.3,\n                outputPrice: 0.6\n            },\n            {\n                id: \"command-nightly\",\n                name: \"Command Nightly\",\n                description: \"Latest experimental features\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    const baseHeaders = provider?.headers || {};\n    // إضافة headers خاصة لكل مزود\n    switch(providerId){\n        case \"anthropic\":\n            return {\n                ...baseHeaders,\n                \"anthropic-version\": \"2023-06-01\"\n            };\n        case \"cohere\":\n            return {\n                ...baseHeaders,\n                \"Cohere-Version\": \"2022-12-06\"\n            };\n        case \"mistral\":\n            return {\n                ...baseHeaders,\n                \"User-Agent\": \"ContextKit/1.0\"\n            };\n        default:\n            return baseHeaders;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/llmProviders.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fvalidate%2Froute&page=%2Fapi%2Fllm%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();