'use client';

import { useState, useEffect } from 'react';
import { useContextStore, ProviderConfig } from '@/store/contextStore';
import { LLM_PROVIDERS_DATABASE, getProviderById } from '@/lib/llmProviders';
import {
  Settings,
  Eye,
  EyeOff,
  Plus,
  Trash2,
  ArrowRight,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Loader2,
  TestTube,
  Database,
  Home
} from 'lucide-react';
import Link from 'next/link';
import ThemeToggle from '@/components/ThemeToggle';
import LanguageToggle from '@/components/LanguageToggle';
import TestAIGeneration from '@/components/TestAIGeneration';

interface ValidationState {
  [providerId: string]: {
    status: 'idle' | 'validating' | 'valid' | 'invalid' | 'error';
    message?: string;
    lastValidated?: Date;
  };
}

export default function SettingsPage() {
  const {
    currentLanguage,
    apiSettings,
    addProvider,
    updateProvider,
    removeProvider,
    validateProvider,
    getProvider
  } = useContextStore();
  
  const isArabic = currentLanguage === 'ar';
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [validationStates, setValidationStates] = useState<ValidationState>({});
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [expandedProviders, setExpandedProviders] = useState<{[key: string]: boolean}>({});
  const [selectedModels, setSelectedModels] = useState<{[key: string]: string[]}>({});
  const [newCustomModel, setNewCustomModel] = useState('');

  // تهيئة النماذج المحددة عند تحميل الصفحة
  useEffect(() => {
    const configuredProviders = apiSettings.providers || [];
    const initialModels: {[key: string]: string[]} = {};
    configuredProviders.forEach(provider => {
      initialModels[provider.id] = provider.selectedModels || [];
    });
    setSelectedModels(initialModels);
  }, [apiSettings.providers]);

  const translations = {
    title: isArabic ? 'إعدادات نماذج الذكاء الاصطناعي' : 'LLM API Settings',
    subtitle: isArabic ? 'قم بتكوين مفاتيح واجهة برمجة التطبيقات ونماذج الذكاء الاصطناعي' : 'Configure your API keys and AI models',
    providers: isArabic ? 'مقدمو خدمات الذكاء الاصطناعي' : 'LLM Providers',
    addProvider: isArabic ? 'إضافة مقدم خدمة جديد' : 'Add Provider',
    apiKey: isArabic ? 'مفتاح واجهة برمجة التطبيقات' : 'API Key',
    baseUrl: isArabic ? 'الرابط الأساسي للخدمة' : 'Base URL',
    testConnection: isArabic ? 'اختبار الاتصال' : 'Test Connection',
    validating: isArabic ? 'جاري التحقق من الاتصال...' : 'Validating...',
    valid: isArabic ? 'الاتصال صحيح' : 'Valid',
    invalid: isArabic ? 'الاتصال غير صحيح' : 'Invalid',
    error: isArabic ? 'حدث خطأ في الاتصال' : 'Error',
    models: isArabic ? 'النماذج المتوفرة' : 'Available Models',
    selectedModels: isArabic ? 'النماذج المختارة' : 'Selected Models',
    addCustomModel: isArabic ? 'إضافة نموذج مخصص' : 'Add Custom Model',
    customModelName: isArabic ? 'اسم النموذج المخصص' : 'Custom Model Name',
    editModels: isArabic ? 'تحرير النماذج' : 'Edit Models',
    saveModels: isArabic ? 'حفظ التغييرات' : 'Save Models',
    noModelsSelected: isArabic ? 'لم يتم اختيار أي نماذج بعد' : 'No models selected',
    cancel: isArabic ? 'إلغاء العملية' : 'Cancel',
    add: isArabic ? 'إضافة' : 'Add',
    backToHome: isArabic ? 'العودة إلى الصفحة الرئيسية' : 'Back to Home',
    active: isArabic ? 'مفعل' : 'Active',
    selectProvider: isArabic ? 'اختر مقدم الخدمة' : 'Select Provider',
    noProvidersConfigured: isArabic ? 'لم يتم تكوين أي مقدم خدمة حتى الآن' : 'No providers configured yet',
    providerAlreadyExists: isArabic ? 'مقدم الخدمة موجود مسبقاً' : 'Provider already exists',
    pleaseSelectProvider: isArabic ? 'يرجى اختيار مقدم خدمة من القائمة' : 'Please select a provider',
    providerNotFound: isArabic ? 'لم يتم العثور على مقدم الخدمة' : 'Provider not found',
    errorAddingProvider: isArabic ? 'حدث خطأ أثناء إضافة مقدم الخدمة' : 'Error adding provider'
  };

  // دوال إدارة النماذج
  const toggleModelSelection = (providerId: string, modelId: string) => {
    setSelectedModels(prev => {
      const currentModels = prev[providerId] || [];
      const isSelected = currentModels.includes(modelId);
      
      return {
        ...prev,
        [providerId]: isSelected 
          ? currentModels.filter(id => id !== modelId)
          : [...currentModels, modelId]
      };
    });
  };

  const addCustomModel = (providerId: string) => {
    if (newCustomModel.trim()) {
      setSelectedModels(prev => {
        const currentModels = prev[providerId] || [];
        return {
          ...prev,
          [providerId]: [...currentModels, newCustomModel.trim()]
        };
      });
      setNewCustomModel('');
    }
  };

  const removeCustomModel = (providerId: string, modelId: string) => {
    setSelectedModels(prev => {
      const currentModels = prev[providerId] || [];
      return {
        ...prev,
        [providerId]: currentModels.filter(id => id !== modelId)
      };
    });
  };

  const saveProviderModels = (providerId: string) => {
    const models = selectedModels[providerId] || [];
    updateProvider(providerId, { selectedModels: models });
    setExpandedProviders(prev => ({ ...prev, [providerId]: false }));
  };

  const handleAddProvider = async () => {
    setErrorMessage('');

    if (!selectedProviderId) {
      setErrorMessage(translations.pleaseSelectProvider);
      return;
    }

    const providerTemplate = getProviderById(selectedProviderId);
    if (!providerTemplate) {
      setErrorMessage(translations.providerNotFound);
      return;
    }

    const existingProvider = getProvider(selectedProviderId);
    if (existingProvider) {
      setErrorMessage(translations.providerAlreadyExists);
      setShowAddProvider(false);
      setSelectedProviderId('');
      return;
    }

    try {
      const newProvider: ProviderConfig = {
        id: selectedProviderId,
        apiKey: '',
        selectedModels: [],
        isEnabled: false,
        validationStatus: 'pending',
        priority: 1,
        isBackup: false
      };

      addProvider(newProvider);
      setShowAddProvider(false);
      setSelectedProviderId('');
      setErrorMessage('');
    } catch (error) {
      console.error('Error adding provider:', error);
      setErrorMessage(translations.errorAddingProvider);
    }
  };

  const handleValidateProvider = async (providerId: string) => {
    setValidationStates(prev => ({
      ...prev,
      [providerId]: { status: 'validating' }
    }));

    try {
      const isValid = await validateProvider(providerId);
      setValidationStates(prev => ({
        ...prev,
        [providerId]: {
          status: isValid ? 'valid' : 'invalid',
          message: isValid ? translations.valid : translations.invalid,
          lastValidated: new Date()
        }
      }));
    } catch (error) {
      setValidationStates(prev => ({
        ...prev,
        [providerId]: {
          status: 'error',
          message: error instanceof Error ? error.message : translations.error
        }
      }));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'validating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'valid':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'invalid':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const configuredProviders = apiSettings.providers || [];
  const availableProviders = LLM_PROVIDERS_DATABASE.filter(
    p => !configuredProviders.some(cp => cp.id === p.id)
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900" dir={isArabic ? 'rtl' : 'ltr'}>
      <style jsx>{`
        /* دعم كامل للغة العربية */
        [dir="rtl"] {
          font-family: 'Tajawal', 'Arial', sans-serif;
        }

        [dir="rtl"] * {
          font-family: 'Tajawal', 'Arial', sans-serif;
          text-align: right;
        }

        /* تخطيط الصفحة الرئيسي */
        [dir="rtl"] .page-container {
          direction: rtl;
        }

        /* رأس الصفحة */
        [dir="rtl"] .header-section {
          direction: rtl;
        }

        [dir="rtl"] .header-content {
          flex-direction: row-reverse;
          justify-content: space-between;
        }

        [dir="rtl"] .header-left {
          flex-direction: row-reverse;
        }

        [dir="rtl"] .header-right {
          flex-direction: row-reverse;
        }

        /* أقسام المحتوى */
        [dir="rtl"] .section-header {
          flex-direction: row-reverse;
          justify-content: space-between;
        }

        [dir="rtl"] .provider-card {
          direction: rtl;
        }

        [dir="rtl"] .provider-header {
          flex-direction: row-reverse;
          justify-content: space-between;
        }

        [dir="rtl"] .provider-info {
          flex-direction: row-reverse;
          text-align: right;
        }

        [dir="rtl"] .provider-controls {
          flex-direction: row-reverse;
        }

        /* النماذج والحقول */
        [dir="rtl"] .form-section {
          direction: rtl;
        }

        [dir="rtl"] .form-row {
          flex-direction: row-reverse;
        }

        [dir="rtl"] .models-section {
          direction: rtl;
        }

        [dir="rtl"] .models-header {
          flex-direction: row-reverse;
          justify-content: space-between;
        }

        [dir="rtl"] .model-item {
          flex-direction: row-reverse;
        }

        [dir="rtl"] .model-tags {
          justify-content: flex-end;
        }

        /* عناصر الإدخال */
        [dir="rtl"] input[type="text"],
        [dir="rtl"] input[type="password"],
        [dir="rtl"] select,
        [dir="rtl"] textarea {
          text-align: right;
          direction: rtl;
          font-family: 'Tajawal', 'Arial', sans-serif;
        }

        /* الأزرار */
        [dir="rtl"] .button-group {
          flex-direction: row-reverse;
        }

        [dir="rtl"] .button-with-icon {
          flex-direction: row-reverse;
        }

        /* النوافذ المنبثقة */
        [dir="rtl"] .modal-content {
          direction: rtl;
          text-align: right;
        }

        [dir="rtl"] .modal-buttons {
          flex-direction: row-reverse;
        }

        /* النصوص */
        [dir="rtl"] .text-content {
          text-align: right;
          direction: rtl;
        }

        [dir="rtl"] .font-arabic {
          font-family: 'Tajawal', 'Arial', sans-serif;
          text-align: right;
        }
      `}</style>

      {/* رأس الصفحة */}
      <div className="header-section bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`header-content flex items-center justify-between h-16 ${isArabic ? 'flex-row-reverse' : ''}`}>
            {/* الجانب الأيمن - العنوان وزر العودة */}
            <div className={`header-left flex items-center gap-4 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <Link
                href="/"
                className={`flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors ${isArabic ? 'flex-row-reverse' : ''}`}
              >
                {isArabic ? (
                  <ArrowRight className="w-5 h-5" />
                ) : (
                  <ArrowRight className="w-5 h-5 rotate-180" />
                )}
                <span className="font-arabic text-content">{translations.backToHome}</span>
              </Link>

              <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <Settings className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-content">
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white font-arabic">
                    {translations.title}
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                    {translations.subtitle}
                  </p>
                </div>
              </div>
            </div>

            {/* الجانب الأيسر - أزرار التحكم */}
            <div className={`header-right flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <LanguageToggle />
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="page-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">

          {/* قسم مقدمي خدمات الذكاء الاصطناعي */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className={`section-header flex items-center justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content">
                  {translations.providers}
                </h2>
                <button
                  onClick={() => {
                    setShowAddProvider(true);
                    setErrorMessage('');
                    setSelectedProviderId('');
                  }}
                  className={`button-with-icon flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic ${isArabic ? 'flex-row-reverse' : ''}`}
                >
                  <Plus className="w-4 h-4" />
                  <span className="text-content">{translations.addProvider}</span>
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {configuredProviders.length === 0 ? (
                <div className="text-center py-8">
                  <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 font-arabic text-content">
                    {translations.noProvidersConfigured}
                  </p>
                </div>
              ) : (
                configuredProviders.map((provider) => {
                  const providerInfo = getProviderById(provider.id);
                  const validationState = validationStates[provider.id];

                  if (!providerInfo) return null;

                  return (
                    <div key={provider.id} className="provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                      <div className={`provider-header flex items-start justify-between mb-6 ${isArabic ? 'flex-row-reverse' : ''}`}>
                        <div className={`provider-info flex items-center gap-4 ${isArabic ? 'flex-row-reverse' : ''}`}>
                          <span className="text-2xl">{providerInfo.icon}</span>
                          <div className="space-y-1 text-content">
                            <h3 className="font-semibold text-gray-900 dark:text-white font-arabic">
                              {providerInfo.name}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                              {providerInfo.description}
                            </p>
                          </div>
                        </div>

                        <div className={`provider-controls flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
                          {getStatusIcon(validationState?.status || 'idle')}

                          {/* مفتاح التفعيل */}
                          <label className={`flex items-center gap-2 ${isArabic ? 'flex-row-reverse' : ''}`}>
                            <input
                              type="checkbox"
                              checked={provider.isEnabled}
                              onChange={(e) => updateProvider(provider.id, { isEnabled: e.target.checked })}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap text-content">
                              {translations.active}
                            </span>
                          </label>

                          <button
                            onClick={() => removeProvider(provider.id)}
                            className="p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                            title={isArabic ? 'حذف مقدم الخدمة' : 'Remove Provider'}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                      <div className="form-section space-y-4">
                        {/* قسم مفاتيح واجهة برمجة التطبيقات */}
                        <div className={`form-row grid grid-cols-1 md:grid-cols-2 gap-6 ${isArabic ? 'flex-row-reverse' : ''}`}>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content">
                              {translations.apiKey}
                            </label>
                            <div className="relative">
                              <input
                                type={showKeys[provider.id] ? 'text' : 'password'}
                                value={provider.apiKey}
                                onChange={(e) => updateProvider(provider.id, { apiKey: e.target.value })}
                                placeholder={providerInfo.apiKeyPlaceholder}
                                className={`w-full px-3 py-2 ${isArabic ? 'pr-10 pl-3' : 'pr-10 pl-3'} border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic`}
                                dir={isArabic ? 'rtl' : 'ltr'}
                              />
                              <button
                                onClick={() => setShowKeys(prev => ({ ...prev, [provider.id]: !prev[provider.id] }))}
                                className={`absolute ${isArabic ? 'left-3' : 'right-3'} top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600`}
                                title={isArabic ? (showKeys[provider.id] ? 'إخفاء المفتاح' : 'إظهار المفتاح') : (showKeys[provider.id] ? 'Hide key' : 'Show key')}
                              >
                                {showKeys[provider.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content">
                              {translations.baseUrl}
                            </label>
                            <input
                              type="text"
                              value={provider.baseUrl || providerInfo.baseUrl}
                              onChange={(e) => updateProvider(provider.id, { baseUrl: e.target.value })}
                              placeholder={providerInfo.baseUrl}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic"
                              dir={isArabic ? 'rtl' : 'ltr'}
                            />
                          </div>
                        </div>

                        <div className={`flex items-center justify-between w-full mt-4 ${isArabic ? '' : ''}`}>
                          <span className="text-sm text-gray-600 dark:text-gray-400 font-arabic text-content">
                            {translations.models}: {providerInfo.models.length}
                          </span>

                          <button
                            onClick={() => handleValidateProvider(provider.id)}
                            disabled={!provider.apiKey || validationState?.status === 'validating'}
                            className={`button-with-icon flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic ${isArabic ? 'flex-row-reverse' : ''}`}
                          >
                            <TestTube className="w-4 h-4" />
                            <span className="text-content">
                              {validationState?.status === 'validating' ? translations.validating : translations.testConnection}
                            </span>
                          </button>
                        </div>

                        {validationState?.message && (
                          <div className={`mt-2 p-2 rounded text-sm text-content ${
                            validationState.status === 'valid'
                              ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                              : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                          }`}>
                            {validationState.message}
                          </div>
                        )}

                        {/* قسم إدارة النماذج */}
                        <div className="models-section mt-4 border-t border-gray-200 dark:border-gray-600 pt-4">
                          <div className={`models-header flex items-center justify-between mb-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white font-arabic text-content">
                              {translations.selectedModels}
                            </h4>
                            <button
                              onClick={() => {
                                setExpandedProviders(prev => ({ ...prev, [provider.id]: !prev[provider.id] }));
                                if (!selectedModels[provider.id]) {
                                  setSelectedModels(prev => ({ ...prev, [provider.id]: provider.selectedModels || [] }));
                                }
                              }}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic text-content"
                            >
                              {expandedProviders[provider.id] ? translations.saveModels : translations.editModels}
                            </button>
                          </div>

                          {/* عرض النماذج المختارة حالياً */}
                          <div className="mb-3">
                            {(provider.selectedModels || []).length === 0 ? (
                              <p className="text-sm text-gray-500 dark:text-gray-400 font-arabic text-content">
                                {translations.noModelsSelected}
                              </p>
                            ) : (
                              <div className={`model-tags flex flex-wrap gap-2 ${isArabic ? 'justify-end' : 'justify-start'}`}>
                                {(provider.selectedModels || []).map((modelId) => (
                                  <span
                                    key={modelId}
                                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic text-content"
                                  >
                                    {modelId}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* إدارة النماذج الموسعة */}
                          {expandedProviders[provider.id] && (
                            <div className="space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              {/* النماذج المتوفرة */}
                              <div>
                                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content">
                                  {translations.models}
                                </h5>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                                  {providerInfo.models.map((model) => (
                                    <label
                                      key={model.id}
                                      className={`model-item flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer ${isArabic ? 'flex-row-reverse' : ''}`}
                                    >
                                      <input
                                        type="checkbox"
                                        checked={(selectedModels[provider.id] || []).includes(model.id)}
                                        onChange={() => toggleModelSelection(provider.id, model.id)}
                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 flex-shrink-0"
                                      />
                                      <div className="flex-1 min-w-0 text-content">
                                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate font-arabic">
                                          {model.name}
                                        </p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate font-arabic">
                                          {model.description}
                                        </p>
                                      </div>
                                    </label>
                                  ))}
                                </div>
                              </div>

                              {/* إدخال نموذج مخصص */}
                              <div>
                                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content">
                                  {translations.addCustomModel}
                                </h5>
                                <div className={`flex gap-2 ${isArabic ? 'flex-row-reverse' : ''}`}>
                                  <input
                                    type="text"
                                    value={newCustomModel}
                                    onChange={(e) => setNewCustomModel(e.target.value)}
                                    placeholder={translations.customModelName}
                                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-arabic"
                                    onKeyDown={(e) => e.key === 'Enter' && addCustomModel(provider.id)}
                                    dir={isArabic ? 'rtl' : 'ltr'}
                                  />
                                  <button
                                    onClick={() => addCustomModel(provider.id)}
                                    disabled={!newCustomModel.trim()}
                                    className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm flex-shrink-0"
                                    title={isArabic ? 'إضافة النموذج' : 'Add Model'}
                                  >
                                    <Plus className="w-4 h-4" />
                                  </button>
                                </div>
                              </div>

                              {/* النماذج المختارة مع خيار الحذف */}
                              {(selectedModels[provider.id] || []).length > 0 && (
                                <div>
                                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content">
                                    {translations.selectedModels}
                                  </h5>
                                  <div className={`model-tags flex flex-wrap gap-2 ${isArabic ? 'justify-end' : 'justify-start'}`}>
                                    {(selectedModels[provider.id] || []).map((modelId) => (
                                      <div
                                        key={modelId}
                                        className={`flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs ${isArabic ? 'flex-row-reverse' : ''}`}
                                      >
                                        <span className="font-arabic text-content">{modelId}</span>
                                        <button
                                          onClick={() => removeCustomModel(provider.id, modelId)}
                                          className="text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400 flex-shrink-0"
                                          title={isArabic ? 'حذف النموذج' : 'Remove Model'}
                                        >
                                          <Trash2 className="w-3 h-3" />
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* زر الحفظ */}
                              <div className={`button-group flex ${isArabic ? 'justify-start' : 'justify-end'}`}>
                                <button
                                  onClick={() => saveProviderModels(provider.id)}
                                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic text-content"
                                >
                                  {translations.saveModels}
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>

          {/* مكون اختبار توليد الذكاء الاصطناعي */}
          <TestAIGeneration />
        </div>
      </div>

      {/* نافذة إضافة مقدم خدمة جديد */}
      {showAddProvider && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4" dir={isArabic ? 'rtl' : 'ltr'}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic text-content">
              {translations.addProvider}
            </h3>

            <div className="space-y-4">
              <select
                value={selectedProviderId}
                onChange={(e) => setSelectedProviderId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic"
                dir={isArabic ? 'rtl' : 'ltr'}
              >
                <option value="">{translations.selectProvider}</option>
                {availableProviders.map(provider => (
                  <option key={provider.id} value={provider.id}>
                    {provider.icon} {provider.name}
                  </option>
                ))}
              </select>
            </div>

            {/* رسالة الخطأ */}
            {errorMessage && (
              <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className={`flex items-center gap-2 ${isArabic ? 'flex-row-reverse' : ''}`}>
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <p className="text-sm text-red-700 dark:text-red-300 font-arabic text-content">
                    {errorMessage}
                  </p>
                </div>
              </div>
            )}

            <div className={`modal-buttons flex gap-3 mt-6 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={() => {
                  setShowAddProvider(false);
                  setErrorMessage('');
                  setSelectedProviderId('');
                }}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic text-content"
              >
                {translations.cancel}
              </button>
              <button
                onClick={handleAddProvider}
                disabled={!selectedProviderId}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic text-content"
              >
                {translations.add}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
